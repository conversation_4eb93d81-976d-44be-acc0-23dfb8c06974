//
//  TypingIndicatorView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/26/25.
//

import SwiftUI

struct TypingIndicatorView: View {
    @State private var animationOffset: CGFloat = 0
    
    var body: some View {
        HStack(alignment: .bottom, spacing: 0) {
            HStack(spacing: 8) {
                // Typing animation dots
                HStack(spacing: 4) {
                    ForEach(0..<3) { index in
                        Circle()
                            .fill(.secondary)
                            .frame(width: 6, height: 6)
                            .scaleEffect(getScale(for: index))
                            .animation(
                                .easeInOut(duration: 0.6)
                                .repeatForever()
                                .delay(Double(index) * 0.2),
                                value: animationOffset
                            )
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(.gray.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 16))
            }
            
            Spacer(minLength: 50)
        }
        .onAppear {
            animationOffset = 1
        }
        .onDisappear {
            animationOffset = 0
        }
    }
    
    private func getScale(for index: Int) -> CGFloat {
        let baseScale: CGFloat = 0.5
        let maxScale: CGFloat = 1.0
        
        if animationOffset == 0 {
            return baseScale
        }
        
        let progress = (animationOffset + Double(index) * 0.2).truncatingRemainder(dividingBy: 1.0)
        let scale = baseScale + (maxScale - baseScale) * sin(progress * .pi)
        
        return scale
    }
}

#Preview {
    VStack {
        TypingIndicatorView()
            .padding()
        
        Spacer()
    }
    .background(Color(.systemBackground))
}