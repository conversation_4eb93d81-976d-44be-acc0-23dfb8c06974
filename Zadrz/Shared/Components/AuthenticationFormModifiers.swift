//
//  AuthenticationFormModifiers.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

// MARK: - Authentication Form Modifiers
extension View {
    /// Applies common authentication form styling and behavior
    func authenticationFormStyle<Field: Hashable>(
        authViewModel: AuthenticationViewModel,
        focusedField: FocusState<Field?>.Binding,
        onAuthenticated: (() -> Void)? = nil
    ) -> some View {
        self
            .onTapGesture {
                focusedField.wrappedValue = nil
            }
            .sensoryFeedback(.success, trigger: authViewModel.successTrigger)
            .sensoryFeedback(.error, trigger: authViewModel.errorTrigger)
            .authenticationAlerts(
                authViewModel: authViewModel,
                onAuthenticated: onAuthenticated
            )
    }
    
    /// Applies authentication alerts and error handling
    private func authenticationAlerts(
        authViewModel: AuthenticationViewModel,
        onAuthenticated: (() -> Void)? = nil
    ) -> some View {
        self
            .alert("Success", isPresented: .constant(authViewModel.showSuccessAlert)) {
                Button("Continue") {
                    onAuthenticated?()
                }
            } message: {
                Text(authViewModel.successMessage ?? "")
            }
            .alert("Error", isPresented: .constant(authViewModel.showErrorAlert)) {
                Button("OK") { }
            } message: {
                Text(authViewModel.errorMessage ?? "")
            }
    }
}

// MARK: - Authentication Field Management
protocol AuthenticationFieldManaging {
    func clearFields()
    func setupInitialFocus()
}

// MARK: - Common Authentication Behavior
struct AuthenticationBehaviorModifier<Field: Hashable>: ViewModifier {
    let authViewModel: AuthenticationViewModel
    @FocusState.Binding var focusedField: Field?
    let initialField: Field
    let onAuthenticated: (() -> Void)?
    let clearFields: () -> Void
    
    func body(content: Content) -> some View {
        content
            .authenticationFormStyle(
                authViewModel: authViewModel,
                focusedField: $focusedField,
                onAuthenticated: onAuthenticated
            )
            .onAppear {
                clearFields()
            }
            .task {
                focusedField = initialField
            }
    }
}

extension View {
    /// Applies common authentication behavior
    func authenticationBehavior<Field: Hashable>(
        authViewModel: AuthenticationViewModel,
        focusedField: FocusState<Field?>.Binding,
        initialField: Field,
        onAuthenticated: (() -> Void)? = nil,
        clearFields: @escaping () -> Void
    ) -> some View {
        self.modifier(AuthenticationBehaviorModifier(
            authViewModel: authViewModel,
            focusedField: focusedField,
            initialField: initialField,
            onAuthenticated: onAuthenticated,
            clearFields: clearFields
        ))
    }
}

// MARK: - Navigation Destination Modifier
struct AuthenticationNavigationModifier: ViewModifier {
    @Binding var navigateToInterests: Bool
    
    func body(content: Content) -> some View {
        content
            .navigationDestination(isPresented: $navigateToInterests) {
                InterestsSelectionView()
            }
    }
}

extension View {
    /// Adds authentication navigation destinations
    func authenticationNavigation(navigateToInterests: Binding<Bool>) -> some View {
        self.modifier(AuthenticationNavigationModifier(navigateToInterests: navigateToInterests))
    }
}

// MARK: - Reusable Authentication Container
struct AuthenticationContainer<Content: View>: View {
    let content: Content
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    var body: some View {
        VStack(spacing: 0) {
            content
        }
    }
}
