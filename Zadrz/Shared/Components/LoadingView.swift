//
//  LoadingView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/28/25.
//

import SwiftUI

// MARK: - Loading View
struct LoadingView: View {
    let message: String?
    let style: LoadingStyle

    init(message: String? = nil, style: LoadingStyle = .standard) {
        self.message = message
        self.style = style
    }

    var body: some View {
        switch style {
        case .standard:
            standardLoadingView
        case .compact:
            compactLoadingView
        case .overlay:
            overlayLoadingView
        case .inline:
            inlineLoadingView
        }
    }

    // MARK: - Private Views

    private var standardLoadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(.accentColor)

            if let message = message {
                Text(message)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var compactLoadingView: some View {
        HStack(spacing: 12) {
            ProgressView()
                .scaleEffect(0.8)

            if let message = message {
                Text(message)
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
        }
        .padding()
    }

    private var overlayLoadingView: some View {
        ZStack {
            Color.black.opacity(0.3)
                .ignoresSafeArea()

            VStack(spacing: 16) {
                ProgressView()
                    .scaleEffect(1.2)
                    .tint(.white)

                if let message = message {
                    Text(message)
                        .font(.subheadline)
                        .foregroundColor(.white)
                }
            }
            .padding()
            .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
        }
    }

    private var inlineLoadingView: some View {
        HStack(spacing: 8) {
            ProgressView()
                .scaleEffect(0.7)

            if let message = message {
                Text(message)
                    .font(.caption2)
                    .foregroundStyle(.secondary)
            }
        }
    }
}

// MARK: - Loading Style
enum LoadingStyle {
    case standard  // Full screen centered
    case compact  // Small horizontal layout
    case overlay  // Full screen overlay with blur
    case inline  // Tiny inline indicator
}

// MARK: - Convenience Extensions
extension LoadingView {
    static var standard: some View {
        LoadingView(style: .standard)
    }

    static func withMessage(_ message: String) -> some View {
        LoadingView(message: message, style: .standard)
    }
}

#Preview {
    VStack(spacing: 40) {
        LoadingView.withMessage("Loading chats...")
            .frame(height: 100)
            .background(Color.gray.opacity(0.1))

        LoadingView(message: "Fetching data...", style: .compact)
            .background(Color.gray.opacity(0.1))

        LoadingView(message: "Processing...", style: .inline)
            .background(Color.gray.opacity(0.1))
    }
    .padding()
}
