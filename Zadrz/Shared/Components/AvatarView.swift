//
//  AvatarView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/28/25.
//

import SwiftUI

// MARK: - Avatar View
/// A reusable avatar component that eliminates duplicate Circle() patterns throughout the app
struct AvatarView: View {
    let size: CGFloat
    let content: AvatarContent
    var backgroundColor: Color = .clear
    var useGradient: Bool = false
    var useMaterial: Bool = false

    var body: some View {
        ZStack {
            if useMaterial {
                Circle()
                    .fill(.regularMaterial)
                    .frame(width: size, height: size)
            } else if useGradient {
                Circle()
                    .fill(Color.accentColor.gradient)
                    .frame(width: size, height: size)
            } else {
                Circle()
                    .fill(backgroundColor)
                    .frame(width: size, height: size)
            }

            switch content {
            case .text(let text, let font):
                Text(text)
                    .font(font ?? fontForSize)
                    .fontWeight(.semibold)
                    .foregroundStyle(.primary)

            case .emoji(let emoji):
                Text(emoji)
                    .font(fontForSize)

            case .systemImage(let name, let color):
                Image(systemName: name)
                    .font(fontForSize.weight(.medium))
                    .foregroundColor(color ?? .primary)
            }
        }
    }

    private var fontForSize: Font {
        switch size {
        case 0..<40:
            return .caption
        case 40..<60:
            return .title3
        case 60..<80:
            return .title
        case 80..<100:
            return .largeTitle
        default:
            return .system(size: size * 0.4)
        }
    }
}

// MARK: - Avatar Content
enum AvatarContent {
    case text(String, font: Font? = nil)
    case emoji(String)
    case systemImage(String, color: Color? = nil)
}

// MARK: - Convenience Initializers
extension AvatarView {
    /// Creates an avatar with initials
    static func initials(_ name: String, size: CGFloat) -> some View {
        let initials = name.split(separator: " ")
            .compactMap { $0.first }
            .map { String($0) }
            .joined()
            .prefix(2)
            .uppercased()

        return AvatarView(
            size: size,
            content: .text(String(initials)),
            useMaterial: true
        )
    }

    /// Creates an avatar with an emoji
    static func emoji(_ emoji: String, size: CGFloat, gradient: Bool = true) -> some View {
        AvatarView(
            size: size,
            content: .emoji(emoji),
            useGradient: gradient
        )
    }

    /// Creates an avatar with a system image
    static func systemImage(
        _ name: String, size: CGFloat, backgroundColor: Color, iconColor: Color? = nil
    ) -> some View {
        AvatarView(
            size: size,
            content: .systemImage(name, color: iconColor),
            backgroundColor: backgroundColor
        )
    }
}

#Preview {
    VStack(spacing: 20) {
        HStack(spacing: 20) {
            AvatarView.initials("John Doe", size: 60)
            AvatarView.emoji("🎯", size: 60)
            AvatarView.systemImage(
                "person.fill", size: 60, backgroundColor: .accentColor, iconColor: .white)
        }

        HStack(spacing: 20) {
            AvatarView(
                size: 80,
                content: .text("AB"),
                useGradient: true
            )

            AvatarView(
                size: 80,
                content: .emoji("🚀"),
                useMaterial: true
            )

            AvatarView(
                size: 80,
                content: .systemImage("star.fill", color: .white),
                backgroundColor: .orange
            )
        }
    }
    .padding()
}
