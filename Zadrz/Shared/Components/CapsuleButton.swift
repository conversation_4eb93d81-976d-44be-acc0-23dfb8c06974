//
//  CapsuleButton.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

/// A customizable capsule button component with support for icons, custom colors, and selection states.
struct CapsuleButton: View {
    // MARK: - Properties
    let title: String
    let icon: String?
    let selectedBackgroundColor: Color
    let unselectedBackgroundColor: Color
    let selectedForegroundColor: Color
    let unselectedForegroundColor: Color
    let borderColor: Color
    let isSelected: Bool
    let action: () -> Void

    // MARK: - Initializers

    /// Creates a capsule button with default styling
    init(
        title: String,
        isSelected: Bool,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = nil
        self.selectedBackgroundColor = .accentColor
        self.unselectedBackgroundColor = .clear
        self.selectedForegroundColor = .black
        self.unselectedForegroundColor = .accentColor
        self.borderColor = .accentColor
        self.isSelected = isSelected
        self.action = action
    }

    /// Creates a capsule button with custom styling and optional icon
    init(
        title: String,
        icon: String? = nil,
        selectedBackgroundColor: Color = .accentColor,
        unselectedBackgroundColor: Color = .clear,
        selectedForegroundColor: Color = .black,
        unselectedForegroundColor: Color = .accentColor,
        borderColor: Color? = nil,
        isSelected: Bool,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = icon
        self.selectedBackgroundColor = selectedBackgroundColor
        self.unselectedBackgroundColor = unselectedBackgroundColor
        self.selectedForegroundColor = selectedForegroundColor
        self.unselectedForegroundColor = unselectedForegroundColor
        self.borderColor = borderColor ?? (isSelected ? selectedBackgroundColor : unselectedForegroundColor)
        self.isSelected = isSelected
        self.action = action
    }

    // MARK: - Body
    var body: some View {
        Button(action: action) {
            buttonContent
                .font(.system(size: 14, weight: .medium))
                .foregroundStyle(isSelected ? selectedForegroundColor : unselectedForegroundColor)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    Capsule()
                        .fill(isSelected ? selectedBackgroundColor : unselectedBackgroundColor)
                        .overlay(
                            Capsule()
                                .stroke(borderColor, lineWidth: 1)
                        )
                )
        }
        .buttonStyle(.plain)
        .sensoryFeedback(.selection, trigger: isSelected)
    }

    // MARK: - Private Views
    @ViewBuilder
    private var buttonContent: some View {
        if let icon = icon {
            Label(title, systemImage: icon)
        } else {
            Text(title)
        }
    }
}

#Preview {
    @Previewable @State var selectedOption = "Option 1"

    VStack(spacing: 20) {
        Text("Capsule Buttons")
            .font(.headline)
            .frame(maxWidth: .infinity, alignment: .leading)

        // Default capsule buttons
        HStack {
            CapsuleButton(title: "Selected", isSelected: true) {}
            CapsuleButton(title: "Unselected", isSelected: false) {}
        }

        // Capsule buttons with icons
        HStack {
            CapsuleButton(
                title: "Favorite",
                icon: "heart.fill",
                isSelected: selectedOption == "Favorite"
            ) {
                selectedOption = "Favorite"
            }

            CapsuleButton(
                title: "Share",
                icon: "square.and.arrow.up",
                isSelected: selectedOption == "Share"
            ) {
                selectedOption = "Share"
            }
        }

        // Custom colored capsule buttons
        HStack {
            CapsuleButton(
                title: "Success",
                icon: "checkmark",
                selectedBackgroundColor: .green,
                selectedForegroundColor: .white,
                unselectedForegroundColor: .green,
                borderColor: .green,
                isSelected: selectedOption == "Success"
            ) {
                selectedOption = "Success"
            }

            CapsuleButton(
                title: "Warning",
                icon: "exclamationmark.triangle",
                selectedBackgroundColor: .orange,
                selectedForegroundColor: .white,
                unselectedForegroundColor: .orange,
                borderColor: .orange,
                isSelected: selectedOption == "Warning"
            ) {
                selectedOption = "Warning"
            }
        }

        Text("Selected: \(selectedOption)")
            .font(.caption)
            .foregroundStyle(.secondary)
    }
    .padding()
}
