//
//  ChatViewModifiers.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

// MARK: - Chat Setup Modifier
struct ChatSetupModifier: ViewModifier {
    let chatService: ChatService
    let authService: AuthenticationService
    let chatId: String
    let onSetup: () async -> Void
    let onMessageNavigation: () -> Void
    let onKeyboardDismiss: () -> Void
    
    func body(content: Content) -> some View {
        content
            .onTapGesture {
                onKeyboardDismiss()
            }
            .task {
                await onSetup()
            }
            .onAppear {
                setupChatListeners()
                onMessageNavigation()
            }
            .onDisappear {
                chatService.removeAllListeners()
            }
    }
    
    private func setupChatListeners() {
        guard let userId = authService.currentUser?.uid else { return }
        chatService.startListeningForNewMessages(userId: userId, chatId: chatId)
    }
}

extension View {
    /// Applies chat setup behavior including listeners and navigation
    func chatSetup(
        chatService: ChatService,
        authService: AuthenticationService,
        chatId: String,
        onSetup: @escaping () async -> Void,
        onMessageNavigation: @escaping () -> Void = {},
        onKeyboardDismiss: @escaping () -> Void = {}
    ) -> some View {
        self.modifier(ChatSetupModifier(
            chatService: chatService,
            authService: authService,
            chatId: chatId,
            onSetup: onSetup,
            onMessageNavigation: onMessageNavigation,
            onKeyboardDismiss: onKeyboardDismiss
        ))
    }
}

// MARK: - Chat Navigation Modifier
struct ChatNavigationModifier: ViewModifier {
    let hero: HeroPersona
    let chatSession: ChatSession
    
    func body(content: Content) -> some View {
        content
            .navigationTitle(hero.displayName)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    ChatToolbarContent(hero: hero, chatSession: chatSession)
                }
            }
    }
}

extension View {
    /// Applies chat navigation styling and toolbar
    func chatNavigation(hero: HeroPersona, chatSession: ChatSession) -> some View {
        self.modifier(ChatNavigationModifier(hero: hero, chatSession: chatSession))
    }
}

// MARK: - Chat Toolbar Content
struct ChatToolbarContent: View {
    let hero: HeroPersona
    let chatSession: ChatSession
    
    var body: some View {
        HStack(spacing: 12) {
            Text(hero.emoji)
                .font(.title2)
            
            NavigationLink(destination: SavedMessagesView()) {
                Image(systemName: "bookmark.circle")
                    .font(.title2)
                    .foregroundColor(.accentColor)
            }
        }
    }
}

// MARK: - Message Input Modifier
struct MessageInputModifier: ViewModifier {
    @FocusState.Binding var isInputFocused: Bool
    let onSend: (String) -> Void
    let isLoading: Bool
    
    func body(content: Content) -> some View {
        content
            .focused($isInputFocused)
            .onSubmit {
                // Handle send action
            }
            .disabled(isLoading)
    }
}

extension View {
    /// Applies message input behavior
    func messageInput(
        isInputFocused: FocusState<Bool>.Binding,
        onSend: @escaping (String) -> Void,
        isLoading: Bool = false
    ) -> some View {
        self.modifier(MessageInputModifier(
            isInputFocused: isInputFocused,
            onSend: onSend,
            isLoading: isLoading
        ))
    }
}

// MARK: - Scroll Behavior Modifier
struct ChatScrollBehaviorModifier: ViewModifier {
    let animationManager: AnimationStateManager
    let messages: [any Identifiable]
    let showTypingIndicator: Bool
    let isInputFocused: Bool
    
    func body(content: Content) -> some View {
        content
            .scrollDismissesKeyboard(.interactively)
            .onChange(of: messages.count) { oldValue, newValue in
                handleMessageCountChange(oldValue: oldValue, newValue: newValue)
            }
            .onChange(of: showTypingIndicator) { _, isShowing in
                handleTypingIndicatorChange(isShowing: isShowing)
            }
            .onChange(of: isInputFocused) { _, isFocused in
                handleInputFocusChange(isFocused: isFocused)
            }
    }
    
    private func handleMessageCountChange(oldValue: Int, newValue: Int) {
        guard newValue > oldValue,
              !animationManager.isScrollingToBottom,
              let lastMessage = messages.last else { return }
        
        animationManager.setScrollingState(true)
        // Scroll logic would be implemented with ScrollViewReader
    }
    
    private func handleTypingIndicatorChange(isShowing: Bool) {
        guard isShowing, !animationManager.isScrollingToBottom else { return }
        
        animationManager.setScrollingState(true)
        // Typing indicator scroll logic
    }
    
    private func handleInputFocusChange(isFocused: Bool) {
        guard isFocused,
              !animationManager.isScrollingToBottom,
              let lastMessage = messages.last else { return }
        
        animationManager.setScrollingState(true)
        // Input focus scroll logic
    }
}

extension View {
    /// Applies optimized chat scroll behavior
    func chatScrollBehavior(
        animationManager: AnimationStateManager,
        messages: [any Identifiable],
        showTypingIndicator: Bool,
        isInputFocused: Bool
    ) -> some View {
        self.modifier(ChatScrollBehaviorModifier(
            animationManager: animationManager,
            messages: messages,
            showTypingIndicator: showTypingIndicator,
            isInputFocused: isInputFocused
        ))
    }
}

// MARK: - Performance Optimized Chat View
struct PerformanceOptimizedChatModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .optimizedScrolling()
            .listRowOptimized()
            .equatable(UUID()) // Prevent unnecessary re-renders
    }
}

extension View {
    /// Applies performance optimizations for chat views
    func optimizedForChat() -> some View {
        self.modifier(PerformanceOptimizedChatModifier())
    }
}
