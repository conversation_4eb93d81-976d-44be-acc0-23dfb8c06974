//
//  FormTextField.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

/// A customizable form text field component with support for secure text entry,
/// password visibility toggle, clear text functionality, and error states.
struct FormTextField: View {
    // MARK: - Properties
    let title: String
    let placeholder: String
    @Binding var text: String
    var isSecure: Bool = false
    var keyboardType: UIKeyboardType = .default
    var autocapitalization: TextInputAutocapitalization = .sentences
    var errorMessage: String? = nil
    var showClearButton: Bool = true

    // MARK: - State
    @State private var isSecureTextVisible = false
    @FocusState private var isFocused: Bool

    // MARK: - Computed Properties
    private var shouldShowClearButton: Bool {
        showClearButton && !text.isEmpty && isFocused
    }

    private var shouldShowPasswordToggle: Bool {
        isSecure
    }

    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            titleView
            textFieldContainer
            errorMessageView
        }
        .animation(.easeInOut(duration: AppConstants.UI.Animation.fastDuration), value: errorMessage)
    }

    // MARK: - Private Views
    private var titleView: some View {
        Text(title)
            .font(.headline)
            .fontWeight(.medium)
            .foregroundStyle(.primary)
    }

    private var textFieldContainer: some View {
        ZStack(alignment: .trailing) {
            textFieldContent
            trailingButtonsContainer
        }
    }

    private var textFieldContent: some View {
        Group {
            if isSecure && !isSecureTextVisible {
                SecureField(placeholder, text: $text)
            } else {
                TextField(placeholder, text: $text)
                    .keyboardType(keyboardType)
                    .textInputAutocapitalization(autocapitalization)
            }
        }
        .focused($isFocused)
        .textFieldStyle(CustomTextFieldStyle(
            isFocused: isFocused,
            hasError: errorMessage != nil,
            hasTrailingButtons: shouldShowClearButton || shouldShowPasswordToggle
        ))
    }

    private var trailingButtonsContainer: some View {
        HStack(spacing: 8) {
            if shouldShowClearButton {
                clearButton
            }

            if shouldShowPasswordToggle {
                passwordToggleButton
            }
        }
        .padding(.trailing, 12)
    }

    private var clearButton: some View {
        Button {
            withAnimation(.easeInOut(duration: AppConstants.UI.Animation.fastDuration)) {
                text = ""
            }
        } label: {
            Image(systemName: "xmark.circle.fill")
                .foregroundStyle(.secondary)
                .font(.system(size: 16))
        }
        .buttonStyle(.plain)
        .accessibilityLabel(isSecure ? "Clear password" : "Clear text")
        .accessibilityHint(isSecure ? "Clears the password field" : "Clears the text field")
    }

    private var passwordToggleButton: some View {
        Button {
            withAnimation(.easeInOut(duration: AppConstants.UI.Animation.fastDuration)) {
                isSecureTextVisible.toggle()
            }
        } label: {
            Image(systemName: isSecureTextVisible ? AppConstants.SystemImages.eyeSlash : AppConstants.SystemImages.eye)
                .foregroundStyle(.secondary)
                .font(.system(size: 16))
        }
        .buttonStyle(.plain)
        .accessibilityLabel(isSecureTextVisible ? "Hide password" : "Show password")
    }

    @ViewBuilder
    private var errorMessageView: some View {
        if let errorMessage = errorMessage {
            Text(errorMessage)
                .font(.caption)
                .foregroundStyle(.red)
                .transition(.opacity)
        }
    }
}

// MARK: - Custom Text Field Style
struct CustomTextFieldStyle: TextFieldStyle {
    let isFocused: Bool
    let hasError: Bool
    let hasTrailingButtons: Bool

    init(isFocused: Bool, hasError: Bool, hasTrailingButtons: Bool = false) {
        self.isFocused = isFocused
        self.hasError = hasError
        self.hasTrailingButtons = hasTrailingButtons
    }

    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .padding(.trailing, hasTrailingButtons ? 44 : 0) // Extra padding for buttons
            .background(.regularMaterial)
            .clipShape(RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius))
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
    }

    private var borderColor: Color {
        if hasError {
            return .red
        } else if isFocused {
            return .accentColor
        } else {
            return .clear
        }
    }

    private var borderWidth: CGFloat {
        hasError || isFocused ? 2 : 0
    }
}

#Preview {
    @Previewable @State var emailText = "<EMAIL>"
    @Previewable @State var passwordText = "password123"
    @Previewable @State var nameText = ""
    @Previewable @State var searchText = "Sample text"

    return VStack(spacing: 20) {
        FormTextField(
            title: "Email",
            placeholder: "Enter your email",
            text: $emailText,
            keyboardType: .emailAddress,
            autocapitalization: .never
        )

        FormTextField(
            title: "Password (with clear button)",
            placeholder: "Enter your password",
            text: $passwordText,
            isSecure: true,
            showClearButton: true
        )

        FormTextField(
            title: "Name",
            placeholder: "Enter your name",
            text: $nameText,
            errorMessage: "This field is required"
        )

        FormTextField(
            title: "Search",
            placeholder: "Search...",
            text: $searchText,
            showClearButton: true
        )

        FormTextField(
            title: "Confirm Password (no clear button)",
            placeholder: "Confirm your password",
            text: $passwordText,
            isSecure: true,
            showClearButton: false
        )

        FormTextField(
            title: "No Clear Button",
            placeholder: "Type here...",
            text: $searchText,
            showClearButton: false
        )
    }
    .padding()
}
