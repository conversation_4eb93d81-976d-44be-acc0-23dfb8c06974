//
//  OptimizedScrollView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

// MARK: - Optimized Scroll View
struct OptimizedScrollView<Content: View>: View {
  let content: Content
  let showsIndicators: Bool
  let onScrollToBottom: (() -> Void)?

  init(
    showsIndicators: Bool = true,
    onScrollToBottom: (() -> Void)? = nil,
    @ViewBuilder content: () -> Content
  ) {
    self.content = content()
    self.showsIndicators = showsIndicators
    self.onScrollToBottom = onScrollToBottom
  }

  var body: some View {
    ScrollView(showsIndicators: showsIndicators) {
      content
    }
    .scrollDismissesKeyboard(.interactively)
    .optimizedScrolling()
  }
}

// MARK: - Chat Messages Scroll View
struct ChatMessagesScrollView<Message: Identifiable>: View {
  let messages: [Message]
  let animationManager: AnimationStateManager
  let messageContent: (Message) -> AnyView

  init(
    messages: [Message],
    animationManager: AnimationStateManager,
    @ViewBuilder messageContent: @escaping (Message) -> AnyView
  ) {
    self.messages = messages
    self.animationManager = animationManager
    self.messageContent = messageContent
  }

  var body: some View {
    ScrollViewReader { proxy in
      OptimizedScrollView {
        LazyVStack(spacing: 8) {
          ForEach(messages) { message in
            messageContent(message)
              .optimizedListRow()
          }
        }
        .padding(.horizontal, 16)
      }
      .onAppear {
        guard let lastMessage = messages.last else { return }
        proxy.scrollTo(lastMessage.id, anchor: .bottom)
      }
      .onChange(of: messages.count) { oldValue, newValue in
        guard newValue > oldValue,
          !animationManager.isScrollingToBottom,
          let lastMessage = messages.last
        else { return }

        animationManager.setScrollingState(true)
        withAnimation(.easeOut(duration: 0.4)) {
          proxy.scrollTo(lastMessage.id, anchor: .bottom)
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
          animationManager.setScrollingState(false)
        }
      }
    }
  }

  // Scroll functions moved to view level to avoid ScrollViewReader type issues
}

// MARK: - Auto-Scrolling Behavior
struct AutoScrollBehavior<ID: Hashable>: ViewModifier {
  let targetId: ID?
  let animationManager: AnimationStateManager
  let shouldScroll: Bool
  let scrollAction: (ID) -> Void

  func body(content: Content) -> some View {
    content
      .onChange(of: targetId) { _, newId in
        guard let newId = newId,
          shouldScroll,
          !animationManager.isScrollingToBottom
        else { return }

        animationManager.setScrollingState(true)
        AnimationUtilities.scrollToBottom(
          messageId: newId,
          scrollAction: scrollAction
        ) {
          animationManager.setScrollingState(false)
        }
      }
  }
}

extension View {
  /// Adds auto-scrolling behavior to a scroll view
  func autoScroll<ID: Hashable>(
    targetId: ID?,
    animationManager: AnimationStateManager,
    shouldScroll: Bool = true,
    scrollAction: @escaping (ID) -> Void
  ) -> some View {
    self.modifier(
      AutoScrollBehavior(
        targetId: targetId,
        animationManager: animationManager,
        shouldScroll: shouldScroll,
        scrollAction: scrollAction
      ))
  }
}

// MARK: - Scroll Performance Optimizations
extension View {
  /// Optimizes scroll view performance for large lists
  func optimizedForLargeList() -> some View {
    self
      .optimizedListRow()
      .optimizedScrolling()
  }

  /// Adds smooth scroll animations
  func smoothScrolling() -> some View {
    self
      .animation(.easeInOut(duration: AppConstants.UI.Animation.defaultDuration), value: UUID())
  }
}

// MARK: - Scroll State Management
@MainActor
@Observable
final class ScrollStateManager {
  var isScrolling = false
  var lastScrollOffset: CGFloat = 0
  var scrollDirection: ScrollDirection = .none

  enum ScrollDirection {
    case up, down, none
  }

  func updateScrollState(offset: CGFloat) {
    let delta = offset - lastScrollOffset

    if abs(delta) > 5 {  // Threshold to avoid jitter
      scrollDirection = delta > 0 ? .down : .up
      isScrolling = true
    } else {
      isScrolling = false
    }

    lastScrollOffset = offset
  }
}

// MARK: - Scroll Offset Tracking
struct ScrollOffsetTracker: ViewModifier {
  let onOffsetChange: (CGFloat) -> Void

  func body(content: Content) -> some View {
    content
      .background(
        GeometryReader { geometry in
          Color.clear
            .preference(
              key: ScrollOffsetPreferenceKey.self,
              value: geometry.frame(in: .named("scroll")).minY
            )
        }
      )
      .onPreferenceChange(ScrollOffsetPreferenceKey.self) { offset in
        onOffsetChange(offset)
      }
  }
}

private struct ScrollOffsetPreferenceKey: PreferenceKey {
  static var defaultValue: CGFloat = 0
  static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
    value = nextValue()
  }
}

extension View {
  /// Tracks scroll offset changes
  func trackScrollOffset(onOffsetChange: @escaping (CGFloat) -> Void) -> some View {
    self.modifier(ScrollOffsetTracker(onOffsetChange: onOffsetChange))
  }
}
