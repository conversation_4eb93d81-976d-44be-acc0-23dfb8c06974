//
//  OpenAIService.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation

// MARK: - OpenAI Service
@MainActor
final class OpenAIService: ObservableObject {
    @Published var isLoading = false
    @Published var error: String?

    private let apiKey = AppConfiguration.APIKeys.openAI
    private let session: URLSession

    init() {
        // Configure URLSession with proper timeout and caching
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30.0
        config.timeoutIntervalForResource = 60.0
        config.requestCachePolicy = .reloadIgnoringLocalCacheData
        self.session = URLSession(configuration: config)

        // Validate API key on initialization
        if apiKey.isEmpty {
            error = "OpenAI API key is not configured"
        }
    }
    
    // MARK: - Chat Completion with Real OpenAI API
    func sendMessage(
        messages: [ChatMessage],
        hero: HeroPerson<PERSON>
    ) async throws -> ChatMessage {
        isLoading = true
        defer { isLoading = false }
        
        error = nil
        
        // Prepare the conversation context for OpenAI
        var openAIMessages: [[String: Any]] = []
        
        // Add system message for hero persona
        openAIMessages.append([
            "role": "system",
            "content": hero.systemPrompt
        ])
        
        // Add conversation history (only user and assistant messages)
        for message in messages {
            if message.messageType != .date {
                let role = message.isFromUser ? "user" : "assistant"
                openAIMessages.append([
                    "role": role,
                    "content": message.text
                ])
            }
        }
        
        let requestBody: [String: Any] = [
            "model": "gpt-4o",
            "messages": openAIMessages,
            "temperature": 0.7,
            "max_tokens": 1000
        ]
        
        do {
            let response = try await makeOpenAIRequest(requestBody: requestBody)
            
            guard let choices = response["choices"] as? [[String: Any]],
                  let firstChoice = choices.first,
                  let message = firstChoice["message"] as? [String: Any],
                  let content = message["content"] as? String else {
                throw OpenAIError.noResponse
            }
            
            let aiMessage = ChatMessage(
                text: content,
                isFromUser: false,
                timestamp: getCurrentTimestamp()
            )
            
            return aiMessage
        } catch {
            self.error = error.localizedDescription
            throw error
        }
    }
    
    // MARK: - Streaming Chat with Real OpenAI API
    func sendMessageStream(
        messages: [ChatMessage],
        hero: HeroPersona
    ) -> AsyncThrowingStream<String, Error> {
        return AsyncThrowingStream { continuation in
            Task { @MainActor in
                isLoading = true
                defer { isLoading = false }
                
                error = nil
                
                // Prepare the conversation context for OpenAI
                var openAIMessages: [[String: Any]] = []
                
                // Add system message for hero persona
                openAIMessages.append([
                    "role": "system",
                    "content": hero.systemPrompt
                ])
                
                // Add conversation history
                for message in messages {
                    if message.messageType != .date {
                        let role = message.isFromUser ? "user" : "assistant"
                        openAIMessages.append([
                            "role": role,
                            "content": message.text
                        ])
                    }
                }
                
                let requestBody: [String: Any] = [
                    "model": "gpt-4o",
                    "messages": openAIMessages,
                    "temperature": 0.5,
                    "max_tokens": 1000,
                    "stream": true
                ]
                
                do {
                    _ = try await makeStreamingOpenAIRequest(requestBody: requestBody) { partialResponse in
                        continuation.yield(partialResponse)
                    }
                    
                    continuation.finish()
                } catch {
                    self.error = error.localizedDescription
                    continuation.finish(throwing: error)
                }
            }
        }
    }
    
    // MARK: - Private Methods
    private func makeOpenAIRequest(requestBody: [String: Any]) async throws -> [String: Any] {
        guard !apiKey.isEmpty else {
            throw OpenAIError.invalidConfiguration
        }

        guard let url = URL(string: "https://api.openai.com/v1/chat/completions") else {
            throw OpenAIError.invalidConfiguration
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        } catch {
            throw OpenAIError.invalidConfiguration
        }

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw OpenAIError.invalidResponse
        }

        guard httpResponse.statusCode == 200 else {
            if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let error = errorData["error"] as? [String: Any],
               let message = error["message"] as? String {
                throw OpenAIError.apiError(message)
            }

            switch httpResponse.statusCode {
            case 401:
                throw OpenAIError.apiError("Invalid API key")
            case 429:
                throw OpenAIError.apiError("Rate limit exceeded. Please try again later.")
            case 500...599:
                throw OpenAIError.apiError("OpenAI server error. Please try again later.")
            default:
                throw OpenAIError.invalidResponse
            }
        }

        guard let result = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            throw OpenAIError.invalidResponse
        }

        return result
    }
    
    private func makeStreamingOpenAIRequest(
        requestBody: [String: Any],
        onPartialResponse: @escaping (String) -> Void
    ) async throws -> String {
        guard !apiKey.isEmpty else {
            throw OpenAIError.invalidConfiguration
        }

        guard let url = URL(string: "https://api.openai.com/v1/chat/completions") else {
            throw OpenAIError.invalidConfiguration
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("text/event-stream", forHTTPHeaderField: "Accept")
        request.setValue("keep-alive", forHTTPHeaderField: "Connection")
        request.setValue("no-cache", forHTTPHeaderField: "Cache-Control")

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        } catch {
            throw OpenAIError.invalidConfiguration
        }

        return try await withCheckedThrowingContinuation { continuation in
            let task = session.dataTask(with: request) { @Sendable data, response, error in
                if let error = error {
                    continuation.resume(throwing: OpenAIError.networkError(error.localizedDescription))
                    return
                }

                guard let httpResponse = response as? HTTPURLResponse else {
                    continuation.resume(throwing: OpenAIError.invalidResponse)
                    return
                }

                guard httpResponse.statusCode == 200 else {
                    if let data = data,
                       let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let error = errorData["error"] as? [String: Any],
                       let message = error["message"] as? String {
                        continuation.resume(throwing: OpenAIError.apiError(message))
                    } else {
                        continuation.resume(throwing: OpenAIError.invalidResponse)
                    }
                    return
                }

                guard let data = data else {
                    continuation.resume(throwing: OpenAIError.noResponse)
                    return
                }

                // Use local variable to avoid sendable closure mutation
                let dataString = String(data: data, encoding: .utf8) ?? ""
                let lines = dataString.components(separatedBy: .newlines)
                var accumulatedResponse = ""

                for line in lines {
                    if line.hasPrefix("data: ") {
                        let jsonString = String(line.dropFirst(6)).trimmingCharacters(in: .whitespacesAndNewlines)

                        if jsonString == "[DONE]" {
                            break
                        }

                        if !jsonString.isEmpty,
                           let jsonData = jsonString.data(using: .utf8),
                           let json = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any],
                           let choices = json["choices"] as? [[String: Any]],
                           let firstChoice = choices.first,
                           let delta = firstChoice["delta"] as? [String: Any],
                           let content = delta["content"] as? String {

                            accumulatedResponse += content

                            // Capture the current response state for the closure
                            let currentResponse = accumulatedResponse
                            Task { @MainActor in
                                onPartialResponse(currentResponse)
                            }
                        }
                    }
                }

                continuation.resume(returning: accumulatedResponse)
            }

            task.resume()
        }
    }
    
    private func getCurrentTimestamp() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: Date())
    }

    // MARK: - API Key Validation
    func validateAPIKey() async -> Bool {
        guard !apiKey.isEmpty else {
            error = "OpenAI API key is not configured"
            return false
        }

        // Test with a minimal request
        let testRequest: [String: Any] = [
            "model": "gpt-4o",
            "messages": [
                ["role": "user", "content": "Hello"]
            ],
            "max_tokens": 1
        ]

        do {
            _ = try await makeOpenAIRequest(requestBody: testRequest)
            error = nil
            return true
        } catch {
            self.error = "API key validation failed: \(error.localizedDescription)"
            return false
        }
    }
}

// MARK: - OpenAI Error
enum OpenAIError: LocalizedError {
    case noResponse
    case invalidConfiguration
    case invalidResponse
    case apiError(String)
    case networkError(String)

    var errorDescription: String? {
        switch self {
        case .noResponse:
            return "No response received from AI"
        case .invalidConfiguration:
            return "OpenAI API is not properly configured. Please check your API key."
        case .invalidResponse:
            return "Invalid response from OpenAI"
        case .apiError(let message):
            return "OpenAI API Error: \(message)"
        case .networkError(let message):
            return "Network Error: \(message)"
        }
    }
}
