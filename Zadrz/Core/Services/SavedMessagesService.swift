//
//  SavedMessagesService.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/28/25.
//

import Foundation
import FirebaseDatabase
import SwiftUI
import FirebaseDatabaseInternal

@MainActor
final class SavedMessagesService: ObservableObject {
    static let shared = SavedMessagesService()

    @Published var savedMessages: [SavedMessage] = []
    @Published var isLoading = false
    @Published var error: String?

    private let database = Database.database().reference()
    private var savedMessagesListener: DatabaseHandle?
    private var currentUserId: String?

    private init() {}

    func configure(for userId: String) {
        #if DEBUG
        print("🔧 SavedMessagesService: Configuring for user: \(userId)")
        #endif

        // Clean up previous listener if exists
        if let listener = savedMessagesListener {
            database.removeObserver(withHandle: listener)
        }

        currentUserId = userId
        setupRealtimeListener(for: userId)
    }

    // MARK: - Realtime Listener

    private func setupRealtimeListener(for userId: String) {
        isLoading = true

        let savedMessagesRef = database.child("users").child(userId).child("savedMessages")

        savedMessagesListener = savedMessagesRef.observe(.value) { [weak self] snapshot in
            Task { @MainActor in
                guard let self = self else { return }

                var messages: [SavedMessage] = []

                for child in snapshot.children.allObjects {
                    if let childSnapshot = child as? DataSnapshot,
                       let messageData = childSnapshot.value as? [String: Any],
                       let savedMessage = SavedMessage.fromFirebaseData(messageData, id: childSnapshot.key) {
                        messages.append(savedMessage)
                    }
                }

                // Sort by savedAt date (newest first)
                messages.sort { $0.savedAt > $1.savedAt }

                self.savedMessages = messages
                self.isLoading = false
                self.error = nil

                // Force UI update to ensure reactive changes
                self.objectWillChange.send()

                #if DEBUG
                print("✅ SavedMessagesService: Loaded \(messages.count) saved messages from Firebase")
                #endif
            }
        } withCancel: { [weak self] error in
            Task { @MainActor in
                guard let self = self else { return }
                self.error = error.localizedDescription
                self.isLoading = false

                #if DEBUG
                print("❌ SavedMessagesService: Firebase listener error: \(error)")
                #endif
            }
        }
    }

    // MARK: - Core Operations

    func saveMessage(
        content: String,
        originalMessageId: String,
        sourceChatId: String,
        sourceHeroName: String,
        sourceHeroEmoji: String,
        originalTimestamp: Date,
        isFromUser: Bool
    ) async throws {
        guard let userId = currentUserId else {
            #if DEBUG
            print("❌ SavedMessagesService: User not configured - currentUserId is nil")
            #endif
            throw SavedMessageError.userNotConfigured
        }

        #if DEBUG
        print("💾 SavedMessagesService: Saving message for user: \(userId)")
        #endif

        // Check if message is already saved
        let existingMessage = savedMessages.first { $0.originalMessageId == originalMessageId }
        if existingMessage != nil {
            throw SavedMessageError.messageAlreadySaved
        }

        let savedMessage = SavedMessage(
            content: content,
            originalMessageId: originalMessageId,
            sourceChatId: sourceChatId,
            sourceHeroName: sourceHeroName,
            sourceHeroEmoji: sourceHeroEmoji,
            originalTimestamp: originalTimestamp,
            isFromUser: isFromUser
        )

        let messageRef = database.child("users").child(userId).child("savedMessages").child(savedMessage.id)

        do {
            try await messageRef.setValue(savedMessage.toFirebaseData())

            #if DEBUG
            print("✅ SavedMessagesService: Message saved successfully to Firebase")
            #endif
        } catch {
            #if DEBUG
            print("❌ SavedMessagesService: Failed to save message to Firebase: \(error)")
            #endif
            throw SavedMessageError.saveFailed(error.localizedDescription)
        }
    }

    func deleteSavedMessage(_ savedMessage: SavedMessage) async throws {
        guard let userId = currentUserId else {
            throw SavedMessageError.userNotConfigured
        }

        let messageRef = database.child("users").child(userId).child("savedMessages").child(savedMessage.id)

        do {
            try await messageRef.removeValue()

            #if DEBUG
            print("✅ SavedMessagesService: Message deleted successfully from Firebase")
            #endif
        } catch {
            #if DEBUG
            print("❌ SavedMessagesService: Failed to delete message from Firebase: \(error)")
            #endif
            throw SavedMessageError.deleteFailed(error.localizedDescription)
        }
    }

    func cleanup() {
        if let listener = savedMessagesListener {
            database.removeObserver(withHandle: listener)
            savedMessagesListener = nil
        }
        currentUserId = nil
        savedMessages.removeAll()

        #if DEBUG
        print("🧹 SavedMessagesService: Cleaned up listeners and data")
        #endif
    }

    func isMessageSaved(_ messageId: String) -> Bool {
        return savedMessages.contains { $0.originalMessageId == messageId }
    }

    func getSavedMessagesCount() -> Int {
        return savedMessages.count
    }

    func findSavedMessage(by originalMessageId: String) -> SavedMessage? {
        return savedMessages.first { $0.originalMessageId == originalMessageId }
    }
}

// MARK: - Error Types
enum SavedMessageError: LocalizedError {
    case userNotConfigured
    case messageAlreadySaved
    case saveFailed(String)
    case deleteFailed(String)

    var errorDescription: String? {
        switch self {
        case .userNotConfigured:
            return "User not configured for saved messages"
        case .messageAlreadySaved:
            return "This message is already saved"
        case .saveFailed(let message):
            return "Failed to save message: \(message)"
        case .deleteFailed(let message):
            return "Failed to delete message: \(message)"
        }
    }
}
