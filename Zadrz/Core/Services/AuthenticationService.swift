//
//  AuthenticationService.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation
import Combine
import FirebaseAuth
import FirebaseDatabase

// MARK: - Authentication State
enum AuthenticationState {
    case pending, authenticated, unauthenticated
}

// MARK: - Authentication Error
enum AuthenticationError: Error, LocalizedError {
    case userNotLoggedIn
    case invalidEmail
    case invalidCredentials
    case networkError
    case weakPassword
    case emailVerificationRequired
    case unknownError
    
    var errorDescription: String? {
        switch self {
        case .userNotLoggedIn:
            return "You need to be logged in to perform this action."
        case .invalidEmail:
            return "Please provide a valid email address."
        case .invalidCredentials:
            return "The email or password is incorrect."
        case .networkError:
            return "Network error. Please check your connection."
        case .weakPassword:
            return "Password should be at least 6 characters long."
        case .emailVerificationRequired:
            return "A verification email has been sent to your new email address. Please verify it to complete the email change."
        case .unknownError:
            return "An unknown error occurred."
        }
    }
}

// MARK: - Authentication Service Protocol
/// Protocol defining the authentication service interface
/// Provides methods for user authentication, account management, and user data operations
protocol AuthenticationServiceProtocol {
    /// Current authentication state publisher
    var authState: CurrentValueSubject<AuthenticationState, Never> { get }

    /// Currently authenticated Firebase user
    var currentUser: User? { get }

    /// Attempts to automatically log in the user if already authenticated
    func autoLogin() async

    /// Logs in a user with email and password
    /// - Parameters:
    ///   - email: User's email address
    ///   - password: User's password
    /// - Throws: AuthenticationError if login fails
    func login(email: String, password: String) async throws

    /// Creates a new user account
    /// - Parameters:
    ///   - email: User's email address
    ///   - fullName: User's full name
    ///   - password: User's password
    /// - Throws: AuthenticationError if account creation fails
    func createAccount(email: String, fullName: String, password: String) async throws

    /// Logs out the current user
    /// - Throws: AuthenticationError if logout fails
    func logout() async throws

    /// Sends a password reset email
    /// - Parameter email: User's email address
    /// - Throws: AuthenticationError if reset fails
    func resetPassword(email: String) async throws

    /// Updates the user's password
    /// - Parameters:
    ///   - currentPassword: Current password for verification
    ///   - newPassword: New password to set
    /// - Throws: AuthenticationError if update fails
    func updatePassword(currentPassword: String, newPassword: String) async throws

    /// Updates the user's email address
    /// - Parameters:
    ///   - currentPassword: Current password for verification
    ///   - newEmail: New email address
    /// - Throws: AuthenticationError if update fails
    func updateEmail(currentPassword: String, newEmail: String) async throws

    /// Deletes the user's account permanently
    /// - Parameter password: Current password for verification
    /// - Throws: AuthenticationError if deletion fails
    func deleteAccount(password: String) async throws

    /// Retrieves the current user's data from the database
    /// - Returns: UserModel if found, nil otherwise
    /// - Throws: AuthenticationError if retrieval fails
    func getCurrentUserData() async throws -> UserModel?

    /// Updates user data in the database
    /// - Parameter updates: Dictionary of fields to update
    /// - Throws: AuthenticationError if update fails
    func updateUserInDatabase(_ updates: [String: Any]) async throws

    /// Resends email verification to the current user
    /// - Throws: AuthenticationError if resend fails
    func resendEmailVerification() async throws
}

// MARK: - Authentication Service Implementation
/// Concrete implementation of AuthenticationServiceProtocol using Firebase Auth
/// Handles all authentication operations and user management
final class AuthenticationService: AuthenticationServiceProtocol, ObservableObject {
    /// Shared singleton instance
    static let shared = AuthenticationService()

    /// Published authentication state for reactive updates
    @Published var authState = CurrentValueSubject<AuthenticationState, Never>(.pending)

    /// Currently authenticated Firebase user
    var currentUser: User? {
        Auth.auth().currentUser
    }

    /// Private initializer to enforce singleton pattern
    private init() {}
    
    func autoLogin() async {
        if Auth.auth().currentUser != nil {
            authState.send(.authenticated)
        } else {
            authState.send(.unauthenticated)
        }
    }
    
    func login(email: String, password: String) async throws {
        try await Auth.auth().signIn(withEmail: email, password: password)
        authState.send(.authenticated)
    }
    
    func createAccount(email: String, fullName: String, password: String) async throws {
        let authResult = try await Auth.auth().createUser(withEmail: email, password: password)
        let id = authResult.user.uid
        
        let user = UserModel(
            id: id,
            fullName: fullName,
            email: email
        )
        
        try await saveUserToDatabase(user)
        authState.send(.authenticated)
    }
    
    func logout() async throws {
        try Auth.auth().signOut()
        authState.send(.unauthenticated)
    }
    
    func resetPassword(email: String) async throws {
        try await Auth.auth().sendPasswordReset(withEmail: email)
    }

    func updateEmail(currentPassword: String, newEmail: String) async throws {
        guard let user = Auth.auth().currentUser, let email = user.email else {
            throw AuthenticationError.userNotLoggedIn
        }

        let credential = EmailAuthProvider.credential(withEmail: email, password: currentPassword)

        do {
            try await user.reauthenticate(with: credential)
            try await user.sendEmailVerification(beforeUpdatingEmail: newEmail)
            try await updateUserInDatabase(["email": newEmail, "emailVerified": false])
        } catch {
            throw mapFirebaseError(error)
        }
    }

    func updatePassword(currentPassword: String, newPassword: String) async throws {
        guard let user = Auth.auth().currentUser, let email = user.email else {
            throw AuthenticationError.userNotLoggedIn
        }

        let credential = EmailAuthProvider.credential(withEmail: email, password: currentPassword)

        do {
            try await user.reauthenticate(with: credential)
            try await user.updatePassword(to: newPassword)
        } catch {
            throw mapFirebaseError(error)
        }
    }

    func deleteAccount(password: String) async throws {
        guard let user = Auth.auth().currentUser, let email = user.email else {
            throw AuthenticationError.userNotLoggedIn
        }

        let credential = EmailAuthProvider.credential(withEmail: email, password: password)

        do {
            try await user.reauthenticate(with: credential)
            try await Database.database().reference()
                .child("users")
                .child(user.uid)
                .removeValue()
            try await user.delete()
            authState.send(.unauthenticated)
        } catch {
            throw mapFirebaseError(error)
        }
    }

    func getCurrentUserData() async throws -> UserModel? {
        guard let currentUser = Auth.auth().currentUser else {
            throw AuthenticationError.userNotLoggedIn
        }

        let snapshot = try await Database.database().reference()
            .child("users")
            .child(currentUser.uid)
            .getData()

        guard let value = snapshot.value as? [String: Any],
              let id = value["id"] as? String,
              let fullName = value["fullName"] as? String,
              let email = value["email"] as? String else {
            return nil
        }

        return UserModel(
            id: id,
            fullName: fullName,
            email: email,
            phoneNumber: value["phoneNumber"] as? String,
            bio: value["bio"] as? String ?? "Hey there! I am using Zadrz.",
            profileImageUrl: value["profileImageUrl"] as? String,
            emailVerified: value["emailVerified"] as? Bool ?? true
        )
    }

    func updateUserInDatabase(_ updates: [String: Any]) async throws {
        guard let currentUser = Auth.auth().currentUser else {
            throw AuthenticationError.userNotLoggedIn
        }

        try await Database.database().reference()
            .child("users")
            .child(currentUser.uid)
            .updateChildValues(updates)
    }

    func resendEmailVerification() async throws {
        guard let user = Auth.auth().currentUser else {
            throw AuthenticationError.userNotLoggedIn
        }

        do {
            try await user.sendEmailVerification()
        } catch {
            throw mapFirebaseError(error)
        }
    }

    // MARK: - Private Methods
    private func saveUserToDatabase(_ user: UserModel) async throws {
        let userDictionary: [String: Any] = [
            "id": user.id,
            "fullName": user.fullName,
            "email": user.email,
            "bio": user.bio,
            "profileImageUrl": user.profileImageUrl ?? "",
            "emailVerified": user.emailVerified,
            "createdAt": ServerValue.timestamp()
        ]

        try await Database.database().reference()
            .child("users")
            .child(user.id)
            .setValue(userDictionary)
    }

    private func mapFirebaseError(_ error: Error) -> Error {
        let nsError = error as NSError

        switch nsError.code {
        case AuthErrorCode.userNotFound.rawValue,
             AuthErrorCode.invalidEmail.rawValue:
            return AuthenticationError.invalidEmail
        case AuthErrorCode.wrongPassword.rawValue,
             AuthErrorCode.invalidCredential.rawValue:
            return AuthenticationError.invalidCredentials
        case AuthErrorCode.networkError.rawValue:
            return AuthenticationError.networkError
        case AuthErrorCode.weakPassword.rawValue:
            return AuthenticationError.weakPassword
        default:
            return AuthenticationError.unknownError
        }
    }
}