//
//  ChatService.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation
import Firebase
import FirebaseDatabase
import Combine

// MARK: - Chat Service
@MainActor
final class ChatService: ObservableObject {

    static let shared = ChatService()

    @Published var chats: [ChatSession] = []
    @Published var currentChatMessages: [FirestoreChatMessage] = []
    @Published var isLoading = false
    @Published var isEmpty = false
    @Published var isLoadingMoreMessages = false
    @Published var hasMoreMessages = true

    private let database = Database.database().reference()
    private let pageSize = 10 // Load 10 messages at a time
    private let chatPageSize = 30 // Load 30 chats at a time
    private var lastChatKey: String?
    private var lastMessageKey: String?
    private var listeners: [DatabaseHandle] = []

    private init() {}
    
    deinit {
        Task {
            await removeAllListeners()
        }
    }
    
    // MARK: - Chat Management
    func loadChats(userId: String) async {
        guard !userId.isEmpty else {
            print("❌ ChatService: Cannot load chats - userId is empty")
            return
        }

        isLoading = true

        do {
            let snapshot = try await database
                .child("users")
                .child(userId)
                .child("chats")
                .queryOrdered(byChild: "lastMessageTimestamp")
                .queryLimited(toLast: UInt(chatPageSize))
                .getData()

            var fetchedChats: [ChatSession] = []

            for child in snapshot.children.allObjects {
                if let childSnapshot = child as? DataSnapshot,
                   let chatData = childSnapshot.value as? [String: Any],
                   let chatSession = ChatSession.fromDictionary(chatData, id: childSnapshot.key) {
                    fetchedChats.append(chatSession)
                }
            }

            // Sort by last message timestamp (newest first)
            chats = fetchedChats.sorted { $0.lastMessageTimestamp > $1.lastMessageTimestamp }
            isEmpty = chats.isEmpty

            #if DEBUG
            print("✅ ChatService: Loaded \(chats.count) chats for user \(userId)")
            #endif

        } catch {
            print("❌ ChatService: Error loading chats: \(error)")
            // Set error state but don't crash the app
            isEmpty = true
        }

        isLoading = false
    }
    
    func loadChatMessages(userId: String, chatId: String, isInitialLoad: Bool = true) async {
        guard !userId.isEmpty, !chatId.isEmpty else {
            print("❌ ChatService: Cannot load messages - userId or chatId is empty")
            return
        }

        if isInitialLoad {
            currentChatMessages = []
            lastMessageKey = nil
            hasMoreMessages = true
        }

        if !hasMoreMessages { return }

        isLoadingMoreMessages = !isInitialLoad

        #if DEBUG
        print("📥 ChatService: Loading messages for chat \(chatId), initial: \(isInitialLoad)")
        #endif
        
        do {
            var query = database
                .child("users")
                .child(userId)
                .child("chats")
                .child(chatId)
                .child("messages")
                .queryOrdered(byChild: "timestamp")
                .queryLimited(toLast: UInt(pageSize))
            
            // If loading more messages, query before the last loaded message timestamp
            if let lastKey = lastMessageKey, !isInitialLoad {
                // Get the timestamp of the last message to query before it
                let lastMessageSnapshot = try await database
                    .child("users")
                    .child(userId)
                    .child("chats")
                    .child(chatId)
                    .child("messages")
                    .child(lastKey)
                    .getData()
                
                if let messageData = lastMessageSnapshot.value as? [String: Any],
                   let timestamp = messageData["timestamp"] as? TimeInterval {
                    query = query.queryEnding(atValue: timestamp - 0.001) // Query before this timestamp
                }
            }
            
            let snapshot = try await query.getData()
            
            var fetchedMessages: [FirestoreChatMessage] = []
            var newLastKey: String?
            
            for child in snapshot.children.allObjects {
                if let childSnapshot = child as? DataSnapshot,
                   let messageData = childSnapshot.value as? [String: Any],
                   let message = FirestoreChatMessage.fromDictionary(messageData, id: childSnapshot.key) {
                    fetchedMessages.append(message)
                    newLastKey = childSnapshot.key
                }
            }
            
            // Sort messages by timestamp (oldest first)
            fetchedMessages.sort { $0.timestamp < $1.timestamp }
            
            if isInitialLoad {
                currentChatMessages = fetchedMessages
            } else {
                // Prepend older messages
                currentChatMessages = fetchedMessages + currentChatMessages
            }
            
            if let firstKey = fetchedMessages.first?.id {
                lastMessageKey = firstKey
            }
            hasMoreMessages = fetchedMessages.count == pageSize
            
        } catch {
            print("Error loading messages: \(error)")
        }
        
        isLoadingMoreMessages = false
    }
    
    func createNewChat(userId: String, hero: HeroPersona) async -> ChatSession {
        let chatId = database.child("users").child(userId).child("chats").childByAutoId().key ?? UUID().uuidString
        let timestamp = Date()
        
        let chatSession = ChatSession(
            id: chatId,
            hero: hero.rawValue,
            createdAt: timestamp,
            messages: [],
            isFavorite: false,
            category: nil,
            tokensUsed: 0,
            lastMessageTimestamp: timestamp.timeIntervalSince1970
        )
        
        // Save to Firebase
        await saveChatToFirebase(userId: userId, chatSession: chatSession)
        
        // Add to local array
        chats.insert(chatSession, at: 0)
        isEmpty = false
        
        return chatSession
    }

    func findExistingChat(for hero: HeroPersona) -> ChatSession? {
        return chats.first { $0.heroPersona == hero }
    }

    func createLocalChat(hero: HeroPersona) -> ChatSession {
        let chatId = UUID().uuidString
        let timestamp = Date()

        return ChatSession(
            id: chatId,
            hero: hero.rawValue,
            createdAt: timestamp,
            messages: [],
            isFavorite: false,
            category: nil,
            tokensUsed: 0,
            lastMessageTimestamp: timestamp.timeIntervalSince1970
        )
    }

    func saveNewChatToFirebase(userId: String, chatSession: ChatSession) async {
        // Save to Firebase
        await saveChatToFirebase(userId: userId, chatSession: chatSession)

        // Add to local array if not already present
        if !chats.contains(where: { $0.id == chatSession.id }) {
            chats.insert(chatSession, at: 0)
            isEmpty = false
        }

        #if DEBUG
        print("✅ ChatService: New chat saved to Firebase and added to local array")
        #endif
    }

    func addMessage(userId: String, chatId: String, message: ChatMessage, tokensUsed: Int = 0) async {
        guard !userId.isEmpty, !chatId.isEmpty else {
            print("❌ ChatService: Cannot add message - userId or chatId is empty")
            return
        }

        let messageId = database
            .child("users")
            .child(userId)
            .child("chats")
            .child(chatId)
            .child("messages")
            .childByAutoId().key ?? UUID().uuidString

        let firestoreMessage = FirestoreChatMessage(
            id: messageId,
            role: message.isFromUser ? "user" : "assistant",
            content: message.text,
            timestamp: Date(),
            isFavorite: false,
            category: nil
        )

        #if DEBUG
        print("💬 ChatService: Adding \(message.isFromUser ? "user" : "AI") message to chat \(chatId)")
        #endif

        // Save message to Firebase
        await saveMessageToFirebase(userId: userId, chatId: chatId, message: firestoreMessage)

        // Update local chat session
        if let index = chats.firstIndex(where: { $0.id == chatId }) {
            chats[index].tokensUsed += tokensUsed
            chats[index].lastMessageTimestamp = Date().timeIntervalSince1970

            // Move chat to top
            let updatedChat = chats[index]
            chats.remove(at: index)
            chats.insert(updatedChat, at: 0)
        }

        // Don't add to currentChatMessages here - let the Firebase listener handle it
        // This prevents duplicate messages
    }
    
    func getChatSession(chatId: String) -> ChatSession? {
        return chats.first { $0.id == chatId }
    }
    
    func updateChatSession(_ chatSession: ChatSession) {
        if let index = chats.firstIndex(where: { $0.id == chatSession.id }) {
            chats[index] = chatSession
            
            // Move updated chat to top
            let updatedChat = chats[index]
            chats.remove(at: index)
            chats.insert(updatedChat, at: 0)
        }
    }
    
    func deleteChat(userId: String, chatId: String) async {
        guard !userId.isEmpty, !chatId.isEmpty else {
            print("❌ ChatService: Cannot delete chat - userId or chatId is empty")
            return
        }

        #if DEBUG
        print("🗑️ ChatService: Deleting chat \(chatId)")
        #endif

        // Remove from Firebase
        do {
            try await database
                .child("users")
                .child(userId)
                .child("chats")
                .child(chatId)
                .removeValue()

            #if DEBUG
            print("✅ ChatService: Chat deleted from Firebase successfully")
            #endif

        } catch {
            print("❌ ChatService: Error deleting chat from Firebase: \(error)")
            // Continue with local deletion even if Firebase fails
        }

        // Remove from local array
        chats.removeAll { $0.id == chatId }

        // Clear current messages if this was the active chat
        if !currentChatMessages.isEmpty && currentChatMessages.first?.id.contains(chatId) == true {
            currentChatMessages.removeAll()
        }

        isEmpty = chats.isEmpty

        #if DEBUG
        print("✅ ChatService: Chat removed from local storage")
        #endif
    }

    func toggleChatFavorite(userId: String, chatId: String) async {
        guard !userId.isEmpty, !chatId.isEmpty else {
            print("❌ ChatService: Cannot toggle favorite - userId or chatId is empty")
            return
        }

        // Find the chat in local array
        guard let index = chats.firstIndex(where: { $0.id == chatId }) else {
            print("❌ ChatService: Chat not found for favorite toggle")
            return
        }

        let newFavoriteStatus = !chats[index].isFavorite

        #if DEBUG
        print("⭐ ChatService: Toggling chat favorite to: \(newFavoriteStatus)")
        #endif

        // Update in Firebase
        do {
            try await database
                .child("users")
                .child(userId)
                .child("chats")
                .child(chatId)
                .child("isFavorite")
                .setValue(newFavoriteStatus)

            // Update local state
            chats[index].isFavorite = newFavoriteStatus

            #if DEBUG
            print("✅ ChatService: Chat favorite status updated successfully")
            #endif

        } catch {
            print("❌ ChatService: Error toggling chat favorite: \(error)")
        }
    }

    func toggleFavorite(userId: String, chatId: String, messageIndex: Int) async {
        guard messageIndex < currentChatMessages.count else { return }
        
        let messageId = currentChatMessages[messageIndex].id
        let newFavoriteStatus = !currentChatMessages[messageIndex].isFavorite
        
        // Update in Firebase
        do {
            try await database
                .child("users")
                .child(userId)
                .child("chats")
                .child(chatId)
                .child("messages")
                .child(messageId)
                .child("isFavorite")
                .setValue(newFavoriteStatus)
            
            // Update local data
            currentChatMessages[messageIndex].isFavorite = newFavoriteStatus
        } catch {
            print("Error toggling favorite: \(error)")
        }
    }
    
    func setMessageCategory(userId: String, chatId: String, messageIndex: Int, category: String?) async {
        guard messageIndex < currentChatMessages.count else { return }
        
        let messageId = currentChatMessages[messageIndex].id
        
        // Update in Firebase
        do {
            try await database
                .child("users")
                .child(userId)
                .child("chats")
                .child(chatId)
                .child("messages")
                .child(messageId)
                .child("category")
                .setValue(category)
            
            // Update local data
            currentChatMessages[messageIndex].category = category
        } catch {
            print("Error setting message category: \(error)")
        }
    }
    
    // MARK: - Real-time Listeners
    func startListeningForNewMessages(userId: String, chatId: String) {
        guard !userId.isEmpty, !chatId.isEmpty else { return }

        let messagesRef = database
            .child("users")
            .child(userId)
            .child("chats")
            .child(chatId)
            .child("messages")

        let handle = messagesRef.observe(.childAdded) { [weak self] snapshot in
            Task { @MainActor in
                if let messageData = snapshot.value as? [String: Any],
                   let message = FirestoreChatMessage.fromDictionary(messageData, id: snapshot.key) {

                    // Enhanced duplicate detection
                    guard let self = self else { return }

                    // Check if message already exists by ID
                    let existsByID = self.currentChatMessages.contains { $0.id == message.id }

                    // Check if message already exists by content and timestamp (for race conditions)
                    let existsByContent = self.currentChatMessages.contains { existingMessage in
                        existingMessage.content == message.content &&
                        existingMessage.role == message.role &&
                        abs(existingMessage.timestamp.timeIntervalSince(message.timestamp)) < 1.0 // Within 1 second
                    }

                    if !existsByID && !existsByContent {
                        // Insert message in correct chronological order
                        let insertIndex = self.currentChatMessages.firstIndex { $0.timestamp > message.timestamp } ?? self.currentChatMessages.count
                        self.currentChatMessages.insert(message, at: insertIndex)

                        #if DEBUG
                        print("📨 ChatService: New message received via real-time listener (ID: \(message.id))")
                        #endif
                    } else {
                        #if DEBUG
                        print("🔄 ChatService: Duplicate message detected, skipping (ID: \(message.id))")
                        #endif
                    }
                }
            }
        }

        listeners.append(handle)
    }

    func startListeningForChatUpdates(userId: String) {
        guard !userId.isEmpty else { return }

        let chatsRef = database
            .child("users")
            .child(userId)
            .child("chats")

        // Listen for chat updates
        let updateHandle = chatsRef.observe(.childChanged) { [weak self] snapshot in
            Task { @MainActor in
                if let chatData = snapshot.value as? [String: Any],
                   let updatedChat = ChatSession.fromDictionary(chatData, id: snapshot.key) {

                    // Update the chat in the local array
                    if let index = self?.chats.firstIndex(where: { $0.id == updatedChat.id }) {
                        self?.chats[index] = updatedChat

                        // Move updated chat to top if it has new messages
                        let chat = self?.chats[index]
                        self?.chats.remove(at: index)
                        if let chat = chat {
                            self?.chats.insert(chat, at: 0)
                        }

                        #if DEBUG
                        print("🔄 ChatService: Chat updated via real-time listener")
                        #endif
                    }
                }
            }
        }

        listeners.append(updateHandle)
    }
    
    func removeAllListeners() {
        for handle in listeners {
            database.removeObserver(withHandle: handle)
        }
        listeners.removeAll()

        #if DEBUG
        print("🔇 ChatService: All Firebase listeners removed")
        #endif
    }

    func clearCurrentMessages() {
        currentChatMessages.removeAll()

        #if DEBUG
        print("🧹 ChatService: Current messages cleared")
        #endif
    }

    func clearAllData() {
        chats.removeAll()
        currentChatMessages.removeAll()
        isEmpty = false
        isLoading = false
        isLoadingMoreMessages = false
        hasMoreMessages = true
        lastChatKey = nil
        lastMessageKey = nil
        removeAllListeners()

        #if DEBUG
        print("🧹 ChatService: All data cleared")
        #endif
    }
    
    // MARK: - Private Methods
    private func saveChatToFirebase(userId: String, chatSession: ChatSession) async {
        let chatData = chatSession.toDictionary()
        
        do {
            try await database
                .child("users")
                .child(userId)
                .child("chats")
                .child(chatSession.id)
                .setValue(chatData)
        } catch {
            print("Error saving chat: \(error)")
        }
    }
    
    private func saveMessageToFirebase(userId: String, chatId: String, message: FirestoreChatMessage) async {
        let messageData = message.dictionary

        do {
            // Use a transaction to ensure data consistency
            try await database
                .child("users")
                .child(userId)
                .child("chats")
                .child(chatId)
                .child("messages")
                .child(message.id)
                .setValue(messageData)

            // Update chat's last message timestamp and preview
            let chatUpdates: [String: Any] = [
                "lastMessageTimestamp": message.timestamp.timeIntervalSince1970,
                "lastMessage": message.content,
                "lastMessageRole": message.role
            ]

            try await database
                .child("users")
                .child(userId)
                .child("chats")
                .child(chatId)
                .updateChildValues(chatUpdates)

            #if DEBUG
            print("✅ ChatService: Message saved to Firebase successfully")
            #endif

        } catch {
            print("❌ ChatService: Error saving message to Firebase: \(error)")
            // TODO: Implement retry logic or offline queue
        }
    }
}

// MARK: - Chat Session Model (Updated)
struct ChatSession: Identifiable, Codable, Hashable {
    let id: String
    let hero: String
    let createdAt: Date
    var messages: [FirestoreChatMessage]
    var isFavorite: Bool
    var category: String?
    var tokensUsed: Int
    var lastMessageTimestamp: TimeInterval
    var lastMessage: String?
    var lastMessageRole: String?

    var heroPersona: HeroPersona? {
        HeroPersona(rawValue: hero)
    }

    var displayLastMessage: String {
        if let lastMessage = lastMessage, !lastMessage.isEmpty {
            return lastMessage
        }

        if let lastMessage = messages.last(where: { $0.role != "system" }) {
            return lastMessage.content
        }

        return "No messages yet"
    }
    
    var lastMessageTime: String {
        let date = Date(timeIntervalSince1970: lastMessageTimestamp)
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    var displayName: String {
        heroPersona?.displayName ?? "Unknown Hero"
    }
    
    var displayEmoji: String {
        heroPersona?.emoji ?? "🤖"
    }
    
    // Convert messages to ChatMessage format for AI service
    var chatMessages: [ChatMessage] {
        return messages.compactMap { firestoreMessage in
            // Skip system messages in conversation context
            guard firestoreMessage.role != "system" else { return nil }
            
            return ChatMessage(
                text: firestoreMessage.content,
                isFromUser: firestoreMessage.role == "user",
                timestamp: getCurrentTimestamp(from: firestoreMessage.timestamp)
            )
        }
    }
    
    private func getCurrentTimestamp(from date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    // MARK: - Firebase Conversion
    func toDictionary() -> [String: Any] {
        return [
            "id": id,
            "hero": hero,
            "createdAt": createdAt.timeIntervalSince1970,
            "isFavorite": isFavorite,
            "category": category as Any,
            "tokensUsed": tokensUsed,
            "lastMessageTimestamp": lastMessageTimestamp,
            "lastMessage": lastMessage as Any,
            "lastMessageRole": lastMessageRole as Any
        ]
    }

    static func fromDictionary(_ dict: [String: Any], id: String) -> ChatSession? {
        guard let hero = dict["hero"] as? String,
              let createdAtTimestamp = dict["createdAt"] as? TimeInterval,
              let isFavorite = dict["isFavorite"] as? Bool,
              let tokensUsed = dict["tokensUsed"] as? Int,
              let lastMessageTimestamp = dict["lastMessageTimestamp"] as? TimeInterval else {
            return nil
        }

        return ChatSession(
            id: id,
            hero: hero,
            createdAt: Date(timeIntervalSince1970: createdAtTimestamp),
            messages: [],
            isFavorite: isFavorite,
            category: dict["category"] as? String,
            tokensUsed: tokensUsed,
            lastMessageTimestamp: lastMessageTimestamp,
            lastMessage: dict["lastMessage"] as? String,
            lastMessageRole: dict["lastMessageRole"] as? String
        )
    }
    
    // MARK: - Hashable Conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: ChatSession, rhs: ChatSession) -> Bool {
        lhs.id == rhs.id
    }
}

// MARK: - Firestore Chat Message (Updated)
struct FirestoreChatMessage: Codable, Identifiable, Hashable {
    var id: String
    let role: String // "user", "assistant", "system"
    let content: String
    let timestamp: Date
    var isFavorite: Bool
    var category: String?
    
    var dictionary: [String: Any] {
        return [
            "id": id,
            "role": role,
            "content": content,
            "timestamp": timestamp.timeIntervalSince1970,
            "isFavorite": isFavorite,
            "category": category as Any
        ]
    }
    
    // MARK: - Firebase Conversion
    static func fromDictionary(_ dict: [String: Any], id: String) -> FirestoreChatMessage? {
        guard let role = dict["role"] as? String,
              let content = dict["content"] as? String,
              let timestampValue = dict["timestamp"] as? TimeInterval,
              let isFavorite = dict["isFavorite"] as? Bool else {
            return nil
        }
        
        return FirestoreChatMessage(
            id: id,
            role: role,
            content: content,
            timestamp: Date(timeIntervalSince1970: timestampValue),
            isFavorite: isFavorite,
            category: dict["category"] as? String
        )
    }
    
    // MARK: - Hashable Conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: FirestoreChatMessage, rhs: FirestoreChatMessage) -> Bool {
        lhs.id == rhs.id
    }
}
