//
//  SavedMessage.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/28/25.
//

import Foundation

final class SavedMessage: ObservableObject, Identifiable {
    let id: String
    let content: String
    let originalMessageId: String
    let sourceChatId: String
    let sourceHeroName: String
    let sourceHeroEmoji: String
    let savedAt: Date
    let originalTimestamp: Date
    let isFromUser: Bool

    init(
        id: String = UUID().uuidString,
        content: String,
        originalMessageId: String,
        sourceChatId: String,
        sourceHeroName: String,
        sourceHeroEmoji: String,
        savedAt: Date = Date(),
        originalTimestamp: Date,
        isFromUser: Bool
    ) {
        self.id = id
        self.content = content
        self.originalMessageId = originalMessageId
        self.sourceChatId = sourceChatId
        self.sourceHeroName = sourceHeroName
        self.sourceHeroEmoji = sourceHeroEmoji
        self.savedAt = savedAt
        self.originalTimestamp = originalTimestamp
        self.isFromUser = isFromUser
    }

    // MARK: - Firebase Conversion

    func toFirebaseData() -> [String: Any] {
        return [
            "content": content,
            "originalMessageId": originalMessageId,
            "sourceChatId": sourceChatId,
            "sourceHeroName": sourceHeroName,
            "sourceHeroEmoji": sourceHeroEmoji,
            "savedAt": savedAt.timeIntervalSince1970,
            "originalTimestamp": originalTimestamp.timeIntervalSince1970,
            "isFromUser": isFromUser
        ]
    }

    static func fromFirebaseData(_ data: [String: Any], id: String) -> SavedMessage? {
        guard let content = data["content"] as? String,
              let originalMessageId = data["originalMessageId"] as? String,
              let sourceChatId = data["sourceChatId"] as? String,
              let sourceHeroName = data["sourceHeroName"] as? String,
              let sourceHeroEmoji = data["sourceHeroEmoji"] as? String,
              let savedAtTimestamp = data["savedAt"] as? TimeInterval,
              let originalTimestamp = data["originalTimestamp"] as? TimeInterval,
              let isFromUser = data["isFromUser"] as? Bool else {
            return nil
        }

        return SavedMessage(
            id: id,
            content: content,
            originalMessageId: originalMessageId,
            sourceChatId: sourceChatId,
            sourceHeroName: sourceHeroName,
            sourceHeroEmoji: sourceHeroEmoji,
            savedAt: Date(timeIntervalSince1970: savedAtTimestamp),
            originalTimestamp: Date(timeIntervalSince1970: originalTimestamp),
            isFromUser: isFromUser
        )
    }
}

// MARK: - SavedMessage Extensions
extension SavedMessage {
    var formattedSavedTime: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: savedAt)
    }
    
    var formattedOriginalTime: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        return formatter.string(from: originalTimestamp)
    }
    
    var displayContent: String {
        return content.count > 100 ? String(content.prefix(100)) + "..." : content
    }
}
