//
//  ViewPerformanceOptimizations.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

// MARK: - Lazy View Loading
struct LazyView<Content: View>: View {
    let build: () -> Content
    
    init(@ViewBuilder _ build: @escaping () -> Content) {
        self.build = build
    }
    
    var body: Content {
        build()
    }
}

// MARK: - Conditional View Rendering
struct ConditionalView<TrueContent: View, FalseContent: View>: View {
    let condition: Bool
    let trueContent: () -> TrueContent
    let falseContent: () -> FalseContent
    
    init(
        _ condition: Bool,
        @ViewBuilder trueContent: @escaping () -> TrueContent,
        @ViewBuilder falseContent: @escaping () -> FalseContent
    ) {
        self.condition = condition
        self.trueContent = trueContent
        self.falseContent = falseContent
    }
    
    var body: some View {
        if condition {
            trueContent()
        } else {
            falseContent()
        }
    }
}

// MARK: - Memoized View
struct MemoizedView<Content: View, Value: Equatable>: View {
    let value: Value
    let content: (Value) -> Content
    
    init(value: Value, @ViewBuilder content: @escaping (Value) -> Content) {
        self.value = value
        self.content = content
    }
    
    var body: some View {
        content(value)
            .equatable(value)
    }
}

// MARK: - Performance Monitoring
@MainActor
@Observable
final class ViewPerformanceMonitor {
    static let shared = ViewPerformanceMonitor()
    
    private var renderTimes: [String: TimeInterval] = [:]
    private var renderCounts: [String: Int] = [:]
    
    private init() {}
    
    func startMeasuring(for viewName: String) {
        renderTimes[viewName] = Date().timeIntervalSince1970
    }
    
    func endMeasuring(for viewName: String) {
        guard let startTime = renderTimes[viewName] else { return }
        let duration = Date().timeIntervalSince1970 - startTime
        
        renderCounts[viewName, default: 0] += 1
        
        #if DEBUG
        print("🎯 View Performance: \(viewName) rendered in \(String(format: "%.2f", duration * 1000))ms (count: \(renderCounts[viewName] ?? 0))")
        #endif
    }
    
    func getStats() -> [(view: String, count: Int, avgTime: TimeInterval)] {
        return renderCounts.compactMap { (view, count) in
            guard let time = renderTimes[view] else { return nil }
            return (view: view, count: count, avgTime: time / Double(count))
        }
    }
}

// MARK: - Performance Measurement Modifier
struct PerformanceMeasurementModifier: ViewModifier {
    let viewName: String
    
    func body(content: Content) -> some View {
        content
            .onAppear {
                ViewPerformanceMonitor.shared.startMeasuring(for: viewName)
            }
            .onDisappear {
                ViewPerformanceMonitor.shared.endMeasuring(for: viewName)
            }
    }
}

extension View {
    /// Measures view performance
    func measurePerformance(viewName: String) -> some View {
        self.modifier(PerformanceMeasurementModifier(viewName: viewName))
    }
    
    /// Creates a lazy-loaded version of the view
    func lazy() -> some View {
        LazyView { self }
    }
    
    /// Creates a memoized version of the view
    func memoized<T: Equatable>(value: T) -> some View {
        MemoizedView(value: value) { _ in self }
    }
    
    /// Conditionally renders content
    func conditional<TrueContent: View, FalseContent: View>(
        _ condition: Bool,
        @ViewBuilder trueContent: @escaping () -> TrueContent,
        @ViewBuilder falseContent: @escaping () -> FalseContent
    ) -> some View {
        ConditionalView(condition, trueContent: trueContent, falseContent: falseContent)
    }
}

// MARK: - Optimized List Performance
struct OptimizedList<Data: RandomAccessCollection, Content: View>: View 
where Data.Element: Identifiable {
    let data: Data
    let content: (Data.Element) -> Content
    
    init(_ data: Data, @ViewBuilder content: @escaping (Data.Element) -> Content) {
        self.data = data
        self.content = content
    }
    
    var body: some View {
        List {
            LazyVStack(spacing: 0) {
                ForEach(data, id: \.id) { item in
                    content(item)
                        .listRowOptimized()
                }
            }
        }
        .listStyle(.plain)
        .optimizedScrolling()
    }
}

// MARK: - Memory Efficient Image Loading
struct OptimizedAsyncImage: View {
    let url: URL?
    let placeholder: AnyView
    let contentMode: ContentMode
    
    init(
        url: URL?,
        contentMode: ContentMode = .fit,
        @ViewBuilder placeholder: () -> some View = { ProgressView() }
    ) {
        self.url = url
        self.contentMode = contentMode
        self.placeholder = AnyView(placeholder())
    }
    
    var body: some View {
        AsyncImage(url: url) { image in
            image
                .resizable()
                .aspectRatio(contentMode: contentMode)
        } placeholder: {
            placeholder
        }
        .clipped()
    }
}

// MARK: - Debounced State Management
@MainActor
@Observable
final class DebouncedState<T: Equatable> {
    private var value: T
    private var debouncedValue: T
    private var debounceTask: Task<Void, Never>?
    
    var currentValue: T { value }
    var debouncedCurrentValue: T { debouncedValue }
    
    init(initialValue: T) {
        self.value = initialValue
        self.debouncedValue = initialValue
    }
    
    func update(_ newValue: T, debounceTime: TimeInterval = 0.3) {
        value = newValue
        
        debounceTask?.cancel()
        debounceTask = Task {
            try? await Task.sleep(for: .seconds(debounceTime))
            
            if !Task.isCancelled {
                debouncedValue = newValue
            }
        }
    }
}

// MARK: - Optimized State Updates
struct OptimizedStateModifier<T: Equatable>: ViewModifier {
    @Binding var state: T
    let debounceTime: TimeInterval
    let onStateChange: (T) -> Void
    
    @State private var debouncedState: DebouncedState<T>
    
    init(
        state: Binding<T>,
        debounceTime: TimeInterval = 0.3,
        onStateChange: @escaping (T) -> Void
    ) {
        self._state = state
        self.debounceTime = debounceTime
        self.onStateChange = onStateChange
        self._debouncedState = State(initialValue: DebouncedState(initialValue: state.wrappedValue))
    }
    
    func body(content: Content) -> some View {
        content
            .onChange(of: state) { _, newValue in
                debouncedState.update(newValue, debounceTime: debounceTime)
            }
            .onChange(of: debouncedState.debouncedCurrentValue) { _, newValue in
                onStateChange(newValue)
            }
    }
}

extension View {
    /// Adds debounced state change handling
    func debouncedStateChange<T: Equatable>(
        of state: Binding<T>,
        debounceTime: TimeInterval = 0.3,
        perform action: @escaping (T) -> Void
    ) -> some View {
        self.modifier(OptimizedStateModifier(
            state: state,
            debounceTime: debounceTime,
            onStateChange: action
        ))
    }
}
