//
//  AnimationUtilities.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

// MARK: - Animation Utilities
@MainActor
final class AnimationUtilities {
    
    /// Performs a delayed action using modern Swift concurrency
    /// Replaces DispatchQueue.main.asyncAfter patterns
    static func performDelayed(
        after duration: TimeInterval,
        action: @escaping @MainActor () -> Void
    ) {
        Task {
            try? await Task.sleep(for: .seconds(duration))
            action()
        }
    }
    
    /// Performs a spring scale animation with completion
    static func performScaleAnimation(
        scale: Binding<CGFloat>,
        targetScale: CGFloat = 1.1,
        duration: TimeInterval = 0.3,
        completion: (@MainActor () -> Void)? = nil
    ) {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6, blendDuration: 0)) {
            scale.wrappedValue = targetScale
        }
        
        performDelayed(after: duration) {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.7, blendDuration: 0)) {
                scale.wrappedValue = 1.0
            }
            completion?()
        }
    }
    
    /// Performs scroll-to-bottom animation with completion
    static func scrollToBottom<ID: Hashable>(
        proxy: Sc<PERSON>ViewReader,
        messageId: ID,
        duration: TimeInterval = 0.4,
        completion: (@MainActor () -> Void)? = nil
    ) {
        withAnimation(.easeOut(duration: duration)) {
            proxy.scrollTo(messageId, anchor: .bottom)
        }
        
        performDelayed(after: duration) {
            completion?()
        }
    }
    
    /// Performs typing indicator scroll animation
    static func scrollToTypingIndicator(
        proxy: ScrollViewReader,
        duration: TimeInterval = 0.3,
        completion: (@MainActor () -> Void)? = nil
    ) {
        withAnimation(.easeOut(duration: duration)) {
            proxy.scrollTo("typing-indicator", anchor: .bottom)
        }
        
        performDelayed(after: duration) {
            completion?()
        }
    }
}

// MARK: - Animation State Manager
@MainActor
@Observable
final class AnimationStateManager {
    var isScrollingToBottom = false
    var animationScale: CGFloat = 1.0
    
    func setScrollingState(_ isScrolling: Bool) {
        isScrollingToBottom = isScrolling
    }
    
    func resetScrollingState(after duration: TimeInterval = 0.4) {
        AnimationUtilities.performDelayed(after: duration) { [weak self] in
            self?.isScrollingToBottom = false
        }
    }
}

// MARK: - View Extensions for Animation
extension View {
    /// Applies optimized scroll-to-bottom behavior
    func optimizedScrollToBottom<ID: Hashable>(
        proxy: ScrollViewReader,
        messageId: ID,
        animationManager: AnimationStateManager
    ) -> some View {
        self.modifier(ScrollToBottomModifier(
            proxy: proxy,
            messageId: messageId,
            animationManager: animationManager
        ))
    }
}

// MARK: - Scroll To Bottom Modifier
private struct ScrollToBottomModifier<ID: Hashable>: ViewModifier {
    let proxy: ScrollViewReader
    let messageId: ID
    let animationManager: AnimationStateManager
    
    func body(content: Content) -> some View {
        content
            .onChange(of: messageId) { _, newMessageId in
                guard !animationManager.isScrollingToBottom else { return }
                
                animationManager.setScrollingState(true)
                AnimationUtilities.scrollToBottom(
                    proxy: proxy,
                    messageId: newMessageId
                ) {
                    animationManager.setScrollingState(false)
                }
            }
    }
}
