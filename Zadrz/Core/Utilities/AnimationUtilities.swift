//
//  AnimationUtilities.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

// MARK: - Animation Utilities
@MainActor
final class AnimationUtilities {
    
    /// Performs a delayed action using modern Swift concurrency
    /// Replaces DispatchQueue.main.asyncAfter patterns
    static func performDelayed(
        after duration: TimeInterval,
        action: @escaping @MainActor () -> Void
    ) {
        Task {
            try? await Task.sleep(for: .seconds(duration))
            action()
        }
    }
    
    /// Performs a spring scale animation with completion
    static func performScaleAnimation(
        scale: Binding<CGFloat>,
        targetScale: CGFloat = 1.1,
        duration: TimeInterval = 0.3,
        completion: (@MainActor () -> Void)? = nil
    ) {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6, blendDuration: 0)) {
            scale.wrappedValue = targetScale
        }
        
        performDelayed(after: duration) {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.7, blendDuration: 0)) {
                scale.wrappedValue = 1.0
            }
            completion?()
        }
    }
    
    /// Performs scroll-to-bottom animation with completion using a scroll action closure
    static func scrollToBottom<ID: Hashable>(
        messageId: ID,
        duration: TimeInterval = 0.4,
        scrollAction: @escaping (ID) -> Void,
        completion: (@MainActor () -> Void)? = nil
    ) {
        withAnimation(.easeOut(duration: duration)) {
            scrollAction(messageId)
        }

        performDelayed(after: duration) {
            completion?()
        }
    }

    /// Performs typing indicator scroll animation using a scroll action closure
    static func scrollToTypingIndicator(
        duration: TimeInterval = 0.3,
        scrollAction: @escaping () -> Void,
        completion: (@MainActor () -> Void)? = nil
    ) {
        withAnimation(.easeOut(duration: duration)) {
            scrollAction()
        }

        performDelayed(after: duration) {
            completion?()
        }
    }
}

// MARK: - Animation State Manager
@MainActor
@Observable
final class AnimationStateManager {
    var isScrollingToBottom = false
    var animationScale: CGFloat = 1.0
    
    func setScrollingState(_ isScrolling: Bool) {
        isScrollingToBottom = isScrolling
    }
    
    func resetScrollingState(after duration: TimeInterval = 0.4) {
        AnimationUtilities.performDelayed(after: duration) { [weak self] in
            self?.isScrollingToBottom = false
        }
    }
}

// MARK: - View Extensions for Animation
extension View {
    /// Applies optimized scroll-to-bottom behavior using closure-based approach
    func optimizedScrollToBottom<ID: Hashable>(
        messageId: ID,
        animationManager: AnimationStateManager,
        scrollAction: @escaping (ID) -> Void
    ) -> some View {
        self.onChange(of: messageId) { _, newMessageId in
            guard !animationManager.isScrollingToBottom else { return }

            animationManager.setScrollingState(true)
            scrollAction(newMessageId)
            animationManager.resetScrollingState()
        }
    }
}
