//
//  ErrorHandlingUtilities.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

// MARK: - Error Handling Protocol
protocol ErrorHandling: ObservableObject {
    var errorMessage: String? { get set }
    var showErrorAlert: Bool { get set }
    var successMessage: String? { get set }
    var showSuccessAlert: Bool { get set }
    var errorTrigger: Bool { get set }
    var successTrigger: Bool { get set }
    
    func showError(_ message: String)
    func showSuccess(_ message: String)
    func clearErrorState()
    func triggerErrorFeedback()
    func triggerSuccessFeedback()
}

// MARK: - Default Error Handling Implementation
extension ErrorHandling {
    func showError(_ message: String) {
        errorMessage = message
        showErrorAlert = true
        triggerErrorFeedback()
    }
    
    func showSuccess(_ message: String) {
        successMessage = message
        showSuccessAlert = true
        triggerSuccessFeedback()
    }
    
    func clearErrorState() {
        errorMessage = nil
        showErro<PERSON><PERSON>lert = false
        successMessage = nil
        showSuccessAlert = false
    }
    
    func triggerErrorFeedback() {
        errorTrigger.toggle()
    }
    
    func triggerSuccessFeedback() {
        successTrigger.toggle()
    }
}

// MARK: - Error Display Modifiers
extension View {
    /// Adds standardized error and success alerts
    func errorHandling<T: ErrorHandling>(
        errorHandler: T,
        onSuccess: (() -> Void)? = nil,
        onError: (() -> Void)? = nil
    ) -> some View {
        self
            .alert("Error", isPresented: .constant(errorHandler.showErrorAlert)) {
                Button("OK") {
                    errorHandler.clearErrorState()
                    onError?()
                }
            } message: {
                Text(errorHandler.errorMessage ?? "An unknown error occurred")
            }
            .alert("Success", isPresented: .constant(errorHandler.showSuccessAlert)) {
                Button("Continue") {
                    errorHandler.clearErrorState()
                    onSuccess?()
                }
            } message: {
                Text(errorHandler.successMessage ?? "Operation completed successfully")
            }
    }
    
    /// Adds haptic feedback for errors and success
    func hapticErrorHandling<T: ErrorHandling>(errorHandler: T) -> some View {
        self
            .sensoryFeedback(
                .error,
                trigger: errorHandler.errorTrigger
            )
            .sensoryFeedback(
                .success,
                trigger: errorHandler.successTrigger
            )
    }
    
    /// Combines error handling with haptic feedback
    func completeErrorHandling<T: ErrorHandling>(
        errorHandler: T,
        onSuccess: (() -> Void)? = nil,
        onError: (() -> Void)? = nil
    ) -> some View {
        self
            .errorHandling(
                errorHandler: errorHandler,
                onSuccess: onSuccess,
                onError: onError
            )
            .hapticErrorHandling(errorHandler: errorHandler)
    }
}

// MARK: - Loading State Management
protocol LoadingStateManaging: ObservableObject {
    var isLoading: Bool { get set }
    
    func setLoading(_ loading: Bool)
    func withLoading<T>(_ operation: () async throws -> T) async rethrows -> T
}

extension LoadingStateManaging {
    func setLoading(_ loading: Bool) {
        isLoading = loading
    }
    
    func withLoading<T>(_ operation: () async throws -> T) async rethrows -> T {
        setLoading(true)
        defer { setLoading(false) }
        return try await operation()
    }
}

// MARK: - Combined State Management
protocol StateManaging: ErrorHandling, LoadingStateManaging {}

// MARK: - Error Recovery Strategies
enum ErrorRecoveryStrategy {
    case retry(action: () async -> Void)
    case fallback(action: () -> Void)
    case ignore
    case navigate(destination: String)
}

struct ErrorRecoveryHandler {
    static func handle(
        error: Error,
        strategy: ErrorRecoveryStrategy,
        errorHandler: any ErrorHandling
    ) async {
        switch strategy {
        case .retry(let action):
            await action()
        case .fallback(let action):
            action()
        case .ignore:
            break
        case .navigate(let destination):
            // Handle navigation - could be implemented with NavigationRouter
            print("Navigate to: \(destination)")
        }
        
        errorHandler.showError(error.localizedDescription)
    }
}

// MARK: - Validation Utilities
struct ValidationUtilities {
    static func validateEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
    
    static func validatePassword(_ password: String) -> (isValid: Bool, message: String?) {
        guard password.count >= 6 else {
            return (false, "Password must be at least 6 characters long")
        }
        return (true, nil)
    }
    
    static func validateFullName(_ name: String) -> (isValid: Bool, message: String?) {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedName.isEmpty else {
            return (false, "Full name is required")
        }
        guard trimmedName.count >= 2 else {
            return (false, "Full name must be at least 2 characters long")
        }
        return (true, nil)
    }
}

// MARK: - Form Validation State
@MainActor
@Observable
final class FormValidationState {
    var isValid = false
    var errors: [String: String] = [:]
    
    func setError(for field: String, message: String?) {
        if let message = message {
            errors[field] = message
        } else {
            errors.removeValue(forKey: field)
        }
        updateValidationState()
    }
    
    func clearErrors() {
        errors.removeAll()
        updateValidationState()
    }
    
    private func updateValidationState() {
        isValid = errors.isEmpty
    }
    
    func errorMessage(for field: String) -> String? {
        return errors[field]
    }
}
