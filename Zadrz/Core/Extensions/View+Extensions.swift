//
//  View+Extensions.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

// MARK: - View Extensions
extension View {

    /// Applies a standard button style with customizable colors
    func buttonStyle(
        backgroundColor: Color = .accentColor,
        foregroundColor: Color = .black,
        isEnabled: Bool = true
    ) -> some View {
        self
            .font(.headline)
            .fontWeight(.semibold)
            .frame(maxWidth: .infinity)
            .frame(height: AppConstants.UI.buttonHeight)
            .foregroundStyle(foregroundColor)
            .background(backgroundColor)
            .clipShape(.capsule)  // Ensure all buttons use capsule shape
            .opacity(isEnabled ? 1.0 : 0.5)
            .disabled(!isEnabled)
    }

    /// Applies Apple Sign In button style following Apple's guidelines
    func appleSignInButtonStyle(
        backgroundColor: Color = .accentColor,
        foregroundColor: Color = .black,
        isEnabled: Bool = true
    ) -> some View {
        self
            .frame(maxWidth: .infinity)
            .frame(height: 52)  // Apple's recommended height
            .foregroundStyle(foregroundColor)
            .background(backgroundColor)
            .clipShape(.capsule)  // Change from RoundedRectangle to capsule for consistency
            .overlay(
                Capsule()  // Change overlay to capsule as well
                    .stroke(
                        backgroundColor == .white ? Color.black.opacity(0.3) : Color.clear,
                        lineWidth: 1)
            )
            .opacity(isEnabled ? 1.0 : 0.5)
            .disabled(!isEnabled)
    }

    /// Applies standard padding
    func standardPadding() -> some View {
        self.padding(.horizontal, AppConstants.UI.padding)
    }

    /// Applies a card-like appearance
    func cardStyle() -> some View {
        self
            .background(.regularMaterial)
            .clipShape(RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius))
            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }

    /// Conditional view modifier
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }

    /// Applies haptic feedback
    func hapticFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle = .medium) -> some View {
        self.onTapGesture {
            let impactFeedback = UIImpactFeedbackGenerator(style: style)
            impactFeedback.impactOccurred()
        }
    }

    /// Applies sensory feedback for success/error states
    func sensoryFeedback(
        success successTrigger: Binding<Bool>,
        error errorTrigger: Binding<Bool>
    ) -> some View {
        self
            .sensoryFeedback(.success, trigger: successTrigger.wrappedValue)
            .sensoryFeedback(.error, trigger: errorTrigger.wrappedValue)
    }

    /// Applies a loading overlay
    func loadingOverlay(isLoading: Bool) -> some View {
        self.overlay {
            if isLoading {
                ZStack {
                    Color.black.opacity(0.3)
                    ProgressView()
                        .scaleEffect(1.2)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                }
                .ignoresSafeArea()
            }
        }
    }
}

// MARK: - String Extensions
extension String {
    var isValidEmail: Bool {
        let emailPredicate = NSPredicate(
            format: "SELF MATCHES %@", AppConstants.Validation.emailRegex)
        return emailPredicate.evaluate(with: self)
    }

    var isValidPassword: Bool {
        return self.count >= AppConstants.Validation.minPasswordLength
    }

    func truncated(to length: Int) -> String {
        if self.count > length {
            return String(self.prefix(length)) + "..."
        }
        return self
    }
}

// MARK: - Color Extensions
extension Color {
    static let primaryBackground = Color(.systemBackground)
    static let secondaryBackground = Color(.secondarySystemBackground)
    static let tertiaryBackground = Color(.tertiarySystemBackground)
}
