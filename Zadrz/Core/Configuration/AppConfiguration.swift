//
//  AppConfiguration.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation

// MARK: - App Configuration
enum AppConfiguration {
    
    // MARK: - API Keys
    enum APIKeys {
        static var openAI: String {
            // Try to get from environment variable first, then fallback to Info.plist
            if let envKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"], !envKey.isEmpty {
                return envKey
            }

            // Fallback to Info.plist for development
            guard let path = Bundle.main.path(forResource: "Info", ofType: "plist"),
                  let plist = NSDictionary(contentsOfFile: path),
                  let apiKey = plist["OPENAI_API_KEY"] as? String,
                  !apiKey.isEmpty else {
                #if DEBUG
                print("⚠️ Warning: OpenAI API key not found. Please add OPENAI_API_KEY to Info.plist or environment variables.")
                #endif
                return ""
            }

            return apiKey
        }
    }
    
    // MARK: - Feature Flags
    enum Features {
        static let enableStreaming = true
        static let enableVoiceMessages = false
        static let enableFileAttachments = false
    }
    
    // MARK: - Token Limits
    enum Tokens {
        static let defaultFreeTokens = 1000
        static let warningThreshold = 100
    }
}
