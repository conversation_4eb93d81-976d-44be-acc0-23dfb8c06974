//
//  ViewModelProtocols.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/28/25.
//

import Combine
import SwiftUI

// MARK: - Loading State Protocol
@MainActor
protocol LoadingStateProtocol: ObservableObject {
    var isLoading: Bool { get set }
    var error: String? { get set }
}

// MARK: - Refreshable Protocol
@MainActor
protocol RefreshableProtocol {
    func refresh() async
}

// MARK: - Paginated Loading Protocol
@MainActor
protocol PaginatedLoadingProtocol: LoadingStateProtocol {
    var isLoadingMore: Bool { get set }
    var hasMoreData: Bool { get set }
    func loadMore() async
}

// MARK: - Authentication State Protocol
@MainActor
protocol AuthenticationStateProtocol: ObservableObject {
    var isAuthenticated: Bool { get }
    var currentUser: UserModel? { get }
}

// MARK: - Chat Protocol
protocol ChatProtocol {
    var id: String { get }
    var displayName: String { get }
    var displayEmoji: String { get }
    var lastMessage: String { get }
    var lastMessageTime: String { get }
}

// MARK: - Message Protocol
protocol MessageProtocol: Identifiable {
    var id: String { get }
    var text: String { get }
    var isFromUser: Bool { get }
    var timestamp: String { get }
}

// MARK: - Hero Protocol
protocol HeroProtocol {
    var displayName: String { get }
    var emoji: String { get }
    var systemPrompt: String { get }
}

// MARK: - Service Protocol
@MainActor
protocol ServiceProtocol {
    associatedtype Model
    func fetch() async throws -> [Model]
    func create(_ model: Model) async throws
    func update(_ model: Model) async throws
    func delete(_ model: Model) async throws
}

// MARK: - Cacheable Protocol
protocol CacheableProtocol {
    associatedtype CacheKey: Hashable
    associatedtype CacheValue

    func cache(_ value: CacheValue, for key: CacheKey)
    func getCached(for key: CacheKey) -> CacheValue?
    func clearCache()
}

// MARK: - Coordinator Protocol
@MainActor
protocol CoordinatorProtocol: ObservableObject {
    associatedtype Route
    var path: NavigationPath { get set }
    func navigate(to route: Route)
    func navigateBack()
    func navigateToRoot()
}
