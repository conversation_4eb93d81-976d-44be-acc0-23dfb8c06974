//
//  EnhancedProtocols.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI
import Combine

// MARK: - View Model Protocol
@MainActor
protocol ViewModelProtocol: ObservableObject, ErrorHandling, LoadingStateManaging {
    associatedtype State
    associatedtype Action
    
    var state: State { get set }
    
    func handle(_ action: Action) async
    func reset()
}

// MARK: - Service Protocol
protocol ServiceProtocol: AnyObject {
    associatedtype Configuration
    associatedtype Error: Swift.Error
    
    var configuration: Configuration { get }
    
    func configure(with configuration: Configuration)
    func reset()
}

// MARK: - Repository Protocol
protocol RepositoryProtocol: AnyObject {
    associatedtype Entity: Identifiable
    associatedtype Filter
    
    func fetch(filter: Filter?) async throws -> [Entity]
    func save(_ entity: Entity) async throws
    func delete(_ entity: Entity) async throws
    func update(_ entity: Entity) async throws
}

// MARK: - Cacheable Protocol
protocol Cacheable: AnyObject {
    associatedtype CacheKey: <PERSON><PERSON>le
    associatedtype CacheValue
    
    var cache: [CacheKey: CacheValue] { get set }
    var cacheLimit: Int { get }
    
    func get(for key: <PERSON><PERSON><PERSON>ey) -> CacheValue?
    func set(_ value: CacheValue, for key: CacheKey)
    func clear()
    func clearExpired()
}

extension Cacheable {
    func get(for key: CacheKey) -> CacheValue? {
        return cache[key]
    }
    
    func set(_ value: CacheValue, for key: CacheKey) {
        if cache.count >= cacheLimit {
            // Remove oldest entry (simple FIFO)
            if let firstKey = cache.keys.first {
                cache.removeValue(forKey: firstKey)
            }
        }
        cache[key] = value
    }
    
    func clear() {
        cache.removeAll()
    }
}

// MARK: - Validatable Protocol
protocol Validatable {
    associatedtype ValidationError: Error
    
    func validate() throws
    var isValid: Bool { get }
}

// MARK: - Configurable Protocol
protocol Configurable {
    associatedtype Configuration
    
    func configure(with configuration: Configuration)
}

// MARK: - Resettable Protocol
protocol Resettable {
    func reset()
}

// MARK: - Identifiable State Protocol
protocol IdentifiableState: Identifiable {
    var id: String { get }
    var timestamp: Date { get }
}

// MARK: - Reactive Service Protocol
protocol ReactiveServiceProtocol: ServiceProtocol {
    associatedtype StatePublisher: Publisher
    
    var statePublisher: StatePublisher { get }
}

// MARK: - Enhanced Authentication Service Protocol
protocol EnhancedAuthenticationServiceProtocol: ReactiveServiceProtocol where StatePublisher.Output == AuthenticationState {
    func login(email: String, password: String) async throws
    func register(email: String, password: String, fullName: String) async throws
    func logout() async throws
    func autoLogin() async
    func resetPassword(email: String) async throws
    func updatePassword(newPassword: String) async throws
    func deleteAccount() async throws
}

// MARK: - Enhanced Chat Service Protocol
protocol EnhancedChatServiceProtocol: ReactiveServiceProtocol, Cacheable
where CacheKey == String, CacheValue == ChatSession {
    func createChat(userId: String, hero: HeroPersona) async -> ChatSession
    func sendMessage(userId: String, chatId: String, content: String, hero: HeroPersona) async throws
    func loadChats(userId: String) async throws
    func deleteChat(userId: String, chatId: String) async throws
}

// MARK: - Message Service Protocol
protocol MessageServiceProtocol: ServiceProtocol {
    func saveMessage(_ message: SavedMessage) async throws
    func loadSavedMessages(userId: String) async throws -> [SavedMessage]
    func deleteMessage(_ messageId: String) async throws
}

// MARK: - View Protocol
protocol ViewProtocol: View {
    associatedtype ViewModel: ViewModelProtocol
    
    var viewModel: ViewModel { get }
}

// MARK: - Coordinator Protocol
protocol CoordinatorProtocol: ObservableObject {
    associatedtype Route
    
    var currentRoute: Route? { get set }
    
    func navigate(to route: Route)
    func goBack()
    func reset()
}

// MARK: - Analytics Protocol
protocol AnalyticsProtocol {
    func track(event: String, parameters: [String: Any]?)
    func setUserProperty(key: String, value: String)
    func setUserId(_ userId: String)
}

// MARK: - Networking Protocol
protocol NetworkingProtocol {
    associatedtype Request
    associatedtype Response
    
    func execute<T: Codable>(_ request: Request) async throws -> T
    func upload<T: Codable>(data: Data, to endpoint: String) async throws -> T
    func download(from url: URL) async throws -> Data
}

// MARK: - Storage Protocol
protocol StorageProtocol {
    func save<T: Codable>(_ object: T, forKey key: String) throws
    func load<T: Codable>(_ type: T.Type, forKey key: String) throws -> T?
    func delete(forKey key: String) throws
    func clear() throws
}

// MARK: - Dependency Injection Protocol
protocol DependencyContainer {
    func register<T>(_ type: T.Type, factory: @escaping () -> T)
    func resolve<T>(_ type: T.Type) -> T?
}

// MARK: - Simple Dependency Container Implementation
@MainActor
final class SimpleDependencyContainer: DependencyContainer {
    static let shared = SimpleDependencyContainer()
    
    private var factories: [String: () -> Any] = [:]
    
    private init() {}
    
    func register<T>(_ type: T.Type, factory: @escaping () -> T) {
        let key = String(describing: type)
        factories[key] = factory
    }
    
    func resolve<T>(_ type: T.Type) -> T? {
        let key = String(describing: type)
        return factories[key]?() as? T
    }
}

// MARK: - Property Wrapper for Dependency Injection
@propertyWrapper
struct Injected<T> {
    private let container: DependencyContainer
    
    init(container: DependencyContainer = SimpleDependencyContainer.shared) {
        self.container = container
    }
    
    var wrappedValue: T {
        guard let dependency = container.resolve(T.self) else {
            fatalError("Dependency \(T.self) not registered")
        }
        return dependency
    }
}

// MARK: - State Machine Protocol
protocol StateMachineProtocol: ObservableObject {
    associatedtype State: Equatable
    associatedtype Event
    
    var currentState: State { get }
    
    func transition(on event: Event) -> State?
    func handle(_ event: Event)
}

// MARK: - Feature Flag Protocol
protocol FeatureFlagProtocol {
    func isEnabled(_ feature: String) -> Bool
    func setEnabled(_ feature: String, enabled: Bool)
    func getAllFlags() -> [String: Bool]
}

// MARK: - Simple Feature Flag Implementation
final class SimpleFeatureFlags: FeatureFlagProtocol {
    static let shared = SimpleFeatureFlags()
    
    private var flags: [String: Bool] = [:]
    
    private init() {}
    
    func isEnabled(_ feature: String) -> Bool {
        return flags[feature] ?? false
    }
    
    func setEnabled(_ feature: String, enabled: Bool) {
        flags[feature] = enabled
    }
    
    func getAllFlags() -> [String: Bool] {
        return flags
    }
}
