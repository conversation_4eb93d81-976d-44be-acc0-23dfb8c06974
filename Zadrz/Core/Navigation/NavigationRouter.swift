//
//  NavigationRouter.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

// MARK: - Navigation Router
@MainActor
final class NavigationRouter: ObservableObject {
  @Published var path = NavigationPath()
  @Published var selectedTab: TabItem = .avatars
  @Published var targetMessageId: String?
  @Published var shouldScrollToMessage = false

  // MARK: - Navigation Methods
  func navigate(to destination: NavigationDestination) {
    path.append(destination)
  }

  func navigateBack() {
    if !path.isEmpty {
      path.removeLast()
    }
  }

  func navigateToRoot() {
    path = NavigationPath()
  }

  func switchTab(to tab: TabItem) {
    selectedTab = tab
    navigateToRoot()  // Clear navigation stack when switching tabs
  }

  // MARK: - Message Navigation
  func navigateToOriginalMessage(chatId: String, messageId: String, chatItem: ChatItem) {
    // First navigate to the chat tab
    selectedTab = .chats

    // Clear current path and navigate to specific chat
    navigateToRoot()
    navigate(to: .chatDetail(chatItem))

    // Set target message for scrolling
    targetMessageId = messageId
    shouldScrollToMessage = true

    #if DEBUG
      print("🧭 NavigationRouter: Navigating to chat \(chatId) and message \(messageId)")
    #endif

    // Trigger scale animation after a brief delay to ensure navigation is complete
    Task {
      try? await Task.sleep(for: .seconds(0.5))
      await MainActor.run {
        self.triggerMessageAnimation(messageId: messageId)
      }
    }
  }

  private func triggerMessageAnimation(messageId: String) {
    NotificationCenter.default.post(
      name: .messageTargeted,
      object: messageId
    )

    #if DEBUG
      print("🎯 NavigationRouter: Triggered animation for message \(messageId)")
    #endif
  }

  func navigateToSavedMessages() {
    navigate(to: .savedMessages)
  }

  func clearMessageTarget() {
    targetMessageId = nil
    shouldScrollToMessage = false
  }

  // MARK: - Deep Linking
  func handleDeepLink(_ url: URL) {
    // TODO: Implement deep linking logic
    print("Handling deep link: \(url)")
  }
}

// MARK: - Navigation Destination
enum NavigationDestination: Hashable {
  // Authentication
  case login
  case signUp
  case forgotPassword

  // Onboarding
  case interests
  case conversationStyle

  // Profile
  case editProfile
  case updateEmail
  case updatePassword
  case deleteAccount
  case profileDetail(ProfileDestination)

  // Chat
  case chatDetail(ChatItem)
  case savedMessages

  // Other
  case avatarSelection
}

// MARK: - Navigation View Builder
struct NavigationDestinationView: View {
  let destination: NavigationDestination
  @EnvironmentObject var authViewModel: AuthenticationViewModel

  var body: some View {
    Group {
      switch destination {
      case .login:
        LoginView(authViewModel: authViewModel)
      case .signUp:
        SignUpView(authViewModel: authViewModel)
      case .forgotPassword:
        ForgotPasswordView(authViewModel: authViewModel)
      case .interests:
        InterestsSelectionView()
      case .conversationStyle:
        ConversationStyleView()
      case .editProfile:
        EditProfileView()
      case .updateEmail:
        UpdateEmailView(authViewModel: authViewModel)
      case .updatePassword:
        UpdatePasswordView(authViewModel: authViewModel)
      case .deleteAccount:
        DeleteAccountView(authViewModel: authViewModel)
      case .profileDetail(let profileDestination):
        // Profile destinations are now handled directly in ProfileOptionsSection
        // This case should not be reached anymore
        Text("Profile feature")
          .navigationTitle(profileDestination.rawValue)
      case .chatDetail(let chatItem):
        ChatDetailView(chat: chatItem)
      case .savedMessages:
        SavedMessagesView()
      case .avatarSelection:
        AvatarSelectionView()
      }
    }
  }
}

// MARK: - Navigation Extensions
extension View {
  func withNavigationDestinations() -> some View {
    self.navigationDestination(for: NavigationDestination.self) { destination in
      NavigationDestinationView(destination: destination)
    }
    .navigationDestination(for: ChatItem.self) { chat in
      NavigationDestinationView(destination: .chatDetail(chat))
    }
  }
}
