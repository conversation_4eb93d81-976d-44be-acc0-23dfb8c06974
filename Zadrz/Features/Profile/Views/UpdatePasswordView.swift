//
//  UpdatePasswordView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct UpdatePasswordView: View {
    @ObservedObject var authViewModel: AuthenticationViewModel
    @Environment(\.dismiss) private var dismiss
    @FocusState private var focusedField: PasswordUpdateField?
    
    var body: some View {
        VStack(spacing: 24) {
            headerSection
            formSection
            Spacer()
            updateButton
        }
        .padding()
        .onAppear {
            focusedField = .currentPassword
        }
        .sensoryFeedback(
            success: $authViewModel.successTrigger,
            error: $authViewModel.errorTrigger
        )
        .alert("Success", isPresented: $authViewModel.showSuccessAlert) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text(authViewModel.successMessage ?? "")
        }
        .alert("Error", isPresented: $authViewModel.showErrorAlert) {
            But<PERSON>("OK", role: .cancel) {}
        } message: {
            Text(authViewModel.errorMessage ?? "")
        }
    }
    
    // MARK: - Private Views
    private var headerSection: some View {
        VStack(spacing: 8) {
            Text("Update your password")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Choose a strong password to keep your account secure")
                .font(.body)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var formSection: some View {
        VStack(spacing: 16) {
            FormTextField(
                title: "Current Password",
                placeholder: "Enter your current password",
                text: $authViewModel.currentPassword,
                isSecure: true
            )
            .focused($focusedField, equals: .currentPassword)
            .onSubmit {
                focusedField = .newPassword
            }
            
            FormTextField(
                title: "New Password",
                placeholder: "At least \(AppConstants.Validation.minPasswordLength) characters",
                text: $authViewModel.newPassword,
                isSecure: true,
                errorMessage: newPasswordErrorMessage
            )
            .focused($focusedField, equals: .newPassword)
            .onSubmit {
                focusedField = .confirmPassword
            }
            
            FormTextField(
                title: "Confirm New Password",
                placeholder: "Re-enter your new password",
                text: $authViewModel.passwordConfirmation,
                isSecure: true,
                errorMessage: confirmPasswordErrorMessage
            )
            .focused($focusedField, equals: .confirmPassword)
            .onSubmit {
                handleUpdatePassword()
            }
        }
    }
    
    private var updateButton: some View {
        PrimaryButton(
            title: "Update Password",
            isLoading: authViewModel.isLoading,
            isEnabled: isFormValid
        ) {
            handleUpdatePassword()
        }
    }
    
    // MARK: - Computed Properties
    private var isFormValid: Bool {
        !authViewModel.currentPassword.isEmpty &&
        !authViewModel.newPassword.isEmpty &&
        authViewModel.newPassword.isValidPassword &&
        !authViewModel.passwordConfirmation.isEmpty &&
        authViewModel.newPassword == authViewModel.passwordConfirmation
    }
    
    private var newPasswordErrorMessage: String? {
        guard !authViewModel.newPassword.isEmpty else { return nil }
        return authViewModel.newPassword.isValidPassword ? nil : "Password must be at least \(AppConstants.Validation.minPasswordLength) characters"
    }
    
    private var confirmPasswordErrorMessage: String? {
        guard !authViewModel.passwordConfirmation.isEmpty else { return nil }
        return authViewModel.newPassword == authViewModel.passwordConfirmation ? nil : "Passwords do not match"
    }
    
    // MARK: - Private Methods
    private func handleUpdatePassword() {
        focusedField = nil
        Task {
            do {
                try await AuthenticationService.shared.updatePassword(
                    currentPassword: authViewModel.currentPassword,
                    newPassword: authViewModel.newPassword
                )
                authViewModel.showSuccess("Password updated successfully!")
                authViewModel.currentPassword = ""
                authViewModel.newPassword = ""
                authViewModel.passwordConfirmation = ""
            } catch {
                authViewModel.showError("Failed to update password: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - Password Update Field
private enum PasswordUpdateField {
    case currentPassword, newPassword, confirmPassword
}

#Preview {
    NavigationStack {
        UpdatePasswordView(authViewModel: AuthenticationViewModel())
    }
}
