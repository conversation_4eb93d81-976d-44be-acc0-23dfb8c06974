//
//  ProfileOptionsSection.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI
import UIKit

// MARK: - Profile Destination
enum ProfileDestination: String, Hashable, CaseIterable {
  case editProfile = "Edit profile"
  case savedChats = "Saved chats"
  case referFriend = "Refer a friend"
  case notification = "Notification"
  case suggestLegend = "Suggest a legend"

  var icon: String {
    switch self {
    case .editProfile: return AppConstants.SystemImages.edit
    case .savedChats: return "message.fill"
    case .referFriend: return "arrowshape.turn.up.right.fill"
    case .notification: return "bell.badge.fill"
    case .suggestLegend: return "star.hexagon.fill"
    }
  }
}

// MARK: - Profile Options Section
struct ProfileOptionsSection: View {
  @Binding var showLogoutAlert: Bool
  let onLogout: () -> Void

  var body: some View {
    Section {
      // Edit Profile
      NavigationLink(destination: EditProfileView()) {
        ProfileItemView(
          labelText: ProfileDestination.editProfile.rawValue,
          labelImage: ProfileDestination.editProfile.icon
        )
      }
      .padding()

      // Saved Messages
      NavigationLink(destination: SavedMessagesView()) {
        ProfileItemView(
          labelText: ProfileDestination.savedChats.rawValue,
          labelImage: ProfileDestination.savedChats.icon
        )
      }
      .padding()

      // Refer a Friend
      ProfileActionItemView(
        destination: .referFriend,
        action: handleReferFriend
      )
      .padding()

      // Suggest a Legend
      ProfileActionItemView(
        destination: .suggestLegend,
        action: handleSuggestLegend
      )
      .padding()

      // Logout Button
      Button(action: { showLogoutAlert = true }) {
        ProfileItemView(
          labelText: AppConstants.Text.Profile.logout,
          labelImage: AppConstants.SystemImages.logout
        )
        .foregroundColor(.red)  // Keep red for logout as it's destructive
      }
      .sensoryFeedback(.warning, trigger: showLogoutAlert)
      .padding()
      .alert("Are you sure you want to logout?", isPresented: $showLogoutAlert) {
        Button("Cancel", role: .cancel, action: {})
        Button(AppConstants.Text.Profile.logout, role: .destructive) {
          UINotificationFeedbackGenerator().notificationOccurred(.success)
          onLogout()
        }
      }
    }
  }

  // MARK: - Private Methods

  private func handleReferFriend() {
    // App Store link for the application
    let appStoreURL = "https://apps.apple.com/app/zadrz/id123456789"  // Replace with actual App Store URL
    let shareText =
      "Check out Zadrz - Chat with legendary figures and gain wisdom from history's greatest minds!"

    let activityViewController = UIActivityViewController(
      activityItems: [shareText, URL(string: appStoreURL)!],
      applicationActivities: nil
    )

    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
      let window = windowScene.windows.first,
      let rootViewController = window.rootViewController
    {

      // For iPad
      if let popover = activityViewController.popoverPresentationController {
        popover.sourceView = rootViewController.view
        popover.sourceRect = CGRect(
          x: rootViewController.view.bounds.midX, y: rootViewController.view.bounds.midY, width: 0,
          height: 0)
        popover.permittedArrowDirections = []
      }

      rootViewController.present(activityViewController, animated: true)
    }
  }

  private func handleSuggestLegend() {
    // Google Form URL for suggesting new hero characters
    let googleFormURL = "https://forms.gle/YourGoogleFormID"  // Replace with actual Google Form URL

    if let url = URL(string: googleFormURL) {
      UIApplication.shared.open(url)
    }
  }
}

// MARK: - Profile Item View
struct ProfileItemView: View {
  let labelText: String
  let labelImage: String

  var body: some View {
    Label(labelText, systemImage: labelImage)
      .font(.body)
  }
}

// MARK: - Profile Action Item View
struct ProfileActionItemView: View {
  let destination: ProfileDestination
  let action: () -> Void

  var body: some View {
    Button(action: action) {
      ProfileItemView(
        labelText: destination.rawValue,
        labelImage: destination.icon
      )
    }
  }
}

// ProfileDetailView removed - replaced with specific implementations

#Preview {
  NavigationStack {
    List {
      ProfileOptionsSection(
        showLogoutAlert: .constant(false),
        onLogout: {}
      )
    }
    .listStyle(.plain)
  }
}
