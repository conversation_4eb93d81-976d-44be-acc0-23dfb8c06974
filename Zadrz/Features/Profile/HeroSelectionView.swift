//
//  HeroSelectionView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct HeroSelectionView: View {
    @ObservedObject private var chatService = ChatService.shared
    @ObservedObject private var tokenManager = TokenManager.shared
    @ObservedObject private var authService = AuthenticationService.shared
    @State private var selectedHero: HeroPersona?
    @State private var showTokenAlert = false
    @State private var navigationPath = NavigationPath()

    // Grid configuration for equal-sized cards
    private let gridColumns = [
        GridItem(.flexible(), spacing: 16),
        GridItem(.flexible(), spacing: 16)
    ]

    var body: some View {
        NavigationStack(path: $navigationPath) {
            ScrollView {
                LazyVGrid(columns: gridColumns, spacing: 20) {
                    ForEach(HeroPersona.allCases) { hero in
                        HeroCard(
                            hero: hero,
                            isSelected: selectedHero == hero,
                            onTap: {
                                selectedHero = hero
                                Task {
                                    await startChatWithHero(hero)
                                    // Reset selection after navigation
                                    await MainActor.run {
                                        selectedHero = nil
                                    }
                                }
                            }
                        )
                        .aspectRatio(0.85, contentMode: .fit) // Ensures equal height
                    }
                }
                .padding(AppConstants.UI.padding)
            }
            .navigationTitle("Choose Your Guide")
            .navigationBarTitleDisplayMode(.large)
            .navigationDestination(for: ChatSession.self) { chatSession in
                ChatDetailView(chatSession: chatSession)
            }
            .onAppear {
                // Reset selection when view appears
                selectedHero = nil
            }
            .task {
                if let userId = authService.currentUser?.uid {
                    await tokenManager.loadUserTokens(userId: userId)
                }
            }
            .alert("Token Limit Reached", isPresented: $showTokenAlert) {
                Button("Subscribe") {
                    Task {
                        if let userId = authService.currentUser?.uid {
                            await tokenManager.setSubscriptionStatus(userId: userId, isSubscribed: true)
                        }
                    }
                }
                Button("Cancel", role: .cancel) {}
            } message: {
                Text("You've used all your free tokens. Subscribe to continue chatting with unlimited access.")
            }
        }
    }
    
    private func startChatWithHero(_ hero: HeroPersona) async {
        guard let userId = authService.currentUser?.uid else { return }

        guard tokenManager.canSendMessage() else {
            showTokenAlert = true
            return
        }

        // Check if chat with this hero already exists
        if let existingChat = chatService.findExistingChat(for: hero) {
            // Navigate to existing chat
            navigationPath.append(existingChat)
        } else {
            // Create new chat session (but don't save to Firebase yet)
            let chatSession = chatService.createLocalChat(hero: hero)

            // Navigate to chat detail
            navigationPath.append(chatSession)
        }
    }
}

// MARK: - Hero Card (Updated with Grid Layout)
struct HeroCard: View {
    let hero: HeroPersona
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 16) {
                // Hero Avatar Section
                VStack(spacing: 12) {
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: heroGradientColors,
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 70, height: 70)
                        
                        Text(hero.emoji)
                            .font(.system(size: 28))
                    }
                    .scaleEffect(isSelected ? 1.1 : 1.0)
                    .animation(.spring(response: 0.3), value: isSelected)
                    
                    // Hero Name
                    Text(hero.displayName)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                        .minimumScaleFactor(0.8)
                }
                
                Spacer(minLength: 8)
                
                // Hero Description
                Text(heroDescription)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
                    .fixedSize(horizontal: false, vertical: true)
                
                Spacer(minLength: 4)
            }
            .padding(16)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.regularMaterial)
                    .stroke(
                        isSelected ? Color.accentColor : Color.clear,
                        lineWidth: 2
                    )
            )
            .overlay(
                // Selection indicator
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        isSelected ? Color.accentColor.opacity(0.3) : Color.clear,
                        lineWidth: 1
                    )
            )
        }
        .buttonStyle(.plain)
        .disabled(isSelected)
    }
    
    private var heroGradientColors: [Color] {
        switch hero {
        case .rumi:
            return [.purple, .blue]
        case .nelsonMandela:
            return [.orange, .red]
        case .leoTolstoy:
            return [.brown, .orange]
        case .martinLutherKing:
            return [.blue, .purple]
        case .saintFrancis:
            return [.green, .mint]
        }
    }
    
    private var heroDescription: String {
        switch hero {
        case .rumi:
            return "Sufi mystic & poet of divine love"
        case .nelsonMandela:
            return "Freedom fighter & reconciler"
        case .leoTolstoy:
            return "Philosopher & moral writer"
        case .martinLutherKing:
            return "Civil rights leader & dreamer"
        case .saintFrancis:
            return "Humble friar & nature mystic"
        }
    }
}

#Preview {
    HeroSelectionView()
}