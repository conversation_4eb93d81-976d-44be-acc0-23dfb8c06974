//
//  SavedMessagesView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/28/25.
//

import SwiftUI

struct SavedMessagesView: View {
    @ObservedObject private var savedMessagesService = SavedMessagesService.shared
    @ObservedObject private var chatService = ChatService.shared
    @ObservedObject private var authService = AuthenticationService.shared
    @EnvironmentObject private var navigationRouter: NavigationRouter
    @State private var showDeleteConfirmation = false
    @State private var messageToDelete: SavedMessage?
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            VStack {
                if savedMessagesService.isLoading {
                    loadingView
                } else if savedMessagesService.savedMessages.isEmpty {
                    emptyStateView
                } else {
                    savedMessagesList
                }
            }
            .navigationTitle("Saved Messages")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Done") {
                        dismiss()
                    }
                    .foregroundColor(.accentColor)
                }
            }
            .alert("Unsave Message", isPresented: $showDeleteConfirmation) {
                Button("Cancel", role: .cancel) { }
                But<PERSON>("Unsave", role: .destructive) {
                    if let message = messageToDelete {
                        Task {
                            try? await savedMessagesService.deleteSavedMessage(message)
                        }
                    }
                    messageToDelete = nil
                }
            } message: {
                Text("Are you sure you want to remove this message from your saved messages?")
            }
        }
    }
    
    // MARK: - Private Views
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Loading saved messages...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            VStack(spacing: 16) {
                Image(systemName: "bookmark.circle")
                    .font(.system(size: 64))
                    .foregroundColor(.secondary)
                
                VStack(spacing: 8) {
                    Text("No Saved Messages")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("Long press on any message in your chats to save it here")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
        }
        .padding(AppConstants.UI.padding)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var savedMessagesList: some View {
        List {
            ForEach(savedMessagesService.savedMessages, id: \.id) { savedMessage in
                SavedMessageRowView(
                    savedMessage: savedMessage,
                    onNavigateToChat: {
                        navigateToOriginalChat(savedMessage)
                    }
                )
                .contextMenu {
                    Button {
                        messageToDelete = savedMessage
                        showDeleteConfirmation = true
                    } label: {
                        Label("Unsave Message", systemImage: "bookmark.slash")
                    }
                }
                .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                    Button {
                        messageToDelete = savedMessage
                        showDeleteConfirmation = true
                    } label: {
                        Label("Unsave", systemImage: "bookmark.slash")
                    }
                    .tint(.red)
                }
            }
        }
        .listStyle(.plain)
        .refreshable {
            // Firebase realtime listener handles loading automatically
        }
    }
    
    // MARK: - Private Methods
    
    private func navigateToOriginalChat(_ savedMessage: SavedMessage) {
        // Find the original chat session
        if let chatSession = chatService.chats.first(where: { $0.id == savedMessage.sourceChatId }) {
            // Convert ChatSession to ChatItem for navigation
            let chatItem = ChatItem(
                name: chatSession.displayName,
                message: chatSession.displayLastMessage,
                time: chatSession.lastMessageTime,
                unreadCount: 0,
                isRead: true
            )

            // Navigate to the original chat and scroll to the specific message
            navigationRouter.navigateToOriginalMessage(
                chatId: savedMessage.sourceChatId,
                messageId: savedMessage.originalMessageId,
                chatItem: chatItem
            )

            // Dismiss this view
            dismiss()
        } else {
            // Handle case where original chat no longer exists
            print("⚠️ Original chat not found for saved message: \(savedMessage.sourceChatId)")
            // TODO: Show alert to user that original chat is no longer available
        }
    }
}

// MARK: - Saved Message Row View
struct SavedMessageRowView: View {
    let savedMessage: SavedMessage
    let onNavigateToChat: () -> Void
    
    var body: some View {
        Button(action: onNavigateToChat) {
            VStack(alignment: .leading, spacing: 12) {
                // Header with hero info and timestamp
                HStack {
                    HStack(spacing: 8) {
                        Text(savedMessage.sourceHeroEmoji)
                            .font(.title2)
                        
                        Text(savedMessage.sourceHeroName)
                            .font(.headline)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("Saved")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        
                        Text(savedMessage.formattedSavedTime)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                // Message content
                Text(savedMessage.displayContent)
                    .font(.body)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.leading)
                    .lineLimit(3)
                
                // Footer with original timestamp and user indicator
                HStack {
                    HStack(spacing: 4) {
                        Image(systemName: savedMessage.isFromUser ? "person.fill" : "brain.head.profile")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(savedMessage.isFromUser ? "You" : savedMessage.sourceHeroName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Text(savedMessage.formattedOriginalTime)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(.plain)
    }
}

#Preview {
    SavedMessagesView()
}
