//
//  MessagesScrollView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

// MARK: - Notification Extensions
extension Notification.Name {
    static let messageTargeted = Notification.Name("messageTargeted")
}

struct MessagesScrollView: View {
    let messages: [ChatMessage]
    let isLoading: Bool
    
    var body: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 12) {
                    if isLoading {
                        loadingIndicator
                    }
                    
                    ForEach(messages) { message in
                        MessageBubbleView(message: message)
                            .id(message.id)
                    }
                }
                .padding(.horizontal, AppConstants.UI.padding)
                .padding(.vertical, AppConstants.UI.padding)
            }
            .defaultScrollAnchor(.bottom)
            .onChange(of: messages.count) { _, _ in
                if let lastMessage = messages.last {
                    withAnimation(.easeInOut(duration: AppConstants.UI.Animation.defaultDuration)) {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
            .onA<PERSON>ar {
                if let lastMessage = messages.last {
                    proxy.scrollTo(lastMessage.id, anchor: .bottom)
                }
            }
        }
    }
    
    // MARK: - Private Views
    private var loadingIndicator: some View {
        HStack {
            ProgressView()
                .scaleEffect(0.8)
            Text("Loading messages...")
                .font(.caption)
                .foregroundStyle(.secondary)
        }
        .padding()
    }
}

// MARK: - Message Bubble View
struct MessageBubbleView: View {
    let message: ChatMessage
    let chatSession: ChatSession?
    let isHighlighted: Bool
    @ObservedObject private var savedMessagesService = SavedMessagesService.shared
    @ObservedObject private var authService = AuthenticationService.shared
    @State private var showSaveAlert = false
    @State private var saveError: String?

    init(message: ChatMessage, chatSession: ChatSession? = nil, isHighlighted: Bool = false) {
        self.message = message
        self.chatSession = chatSession
        self.isHighlighted = isHighlighted
    }

    var body: some View {
        HStack {
            switch message.messageType {
            case .text, .file, .image:
                if message.isFromUser {
                    Spacer()
                    MessageContent(message: message)
                        .contextMenu {
                            saveMessageButton
                        }
                } else {
                    MessageContent(message: message)
                        .contextMenu {
                            saveMessageButton
                        }
                    Spacer()
                }
            case .date:
                DateSeparatorView(message: message)
            }
        }
        .padding(.vertical, message.isFromUser ? 0 : 4)
        .background(
            // Highlight effect for targeted messages
            isHighlighted ?
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.accentColor.opacity(0.2))
                .animation(.easeInOut(duration: 0.3), value: isHighlighted)
            : nil
        )
        .alert("Message Action", isPresented: $showSaveAlert) {
            Button("OK") { }
        } message: {
            if let error = saveError {
                Text(error)
            } else {
                // Check the current saved state to show appropriate message
                let isCurrentlySaved = savedMessagesService.savedMessages.contains { savedMessage in
                    savedMessage.originalMessageId == message.id
                }
                Text(isCurrentlySaved ? "Message saved successfully!" : "Message removed from saved messages!")
            }
        }
    }

    private var saveMessageButton: some View {
        // Check if message is already saved by looking for it in saved messages
        let isAlreadySaved = savedMessagesService.savedMessages.contains { savedMessage in
            savedMessage.originalMessageId == message.id
        }

        return Button {
            Task {
                if isAlreadySaved {
                    await unsaveMessage()
                } else {
                    await saveMessage()
                }
            }
        } label: {
            if isAlreadySaved {
                Label("Unsave Message", systemImage: "bookmark.fill")
            } else {
                Label("Save Message", systemImage: "bookmark")
            }
        }
    }

    private func saveMessage() async {
        // Check authentication first
        guard authService.currentUser != nil else {
            saveError = "You must be logged in to save messages"
            showSaveAlert = true
            return
        }
        
        guard let currentChatSession = chatSession else {
            saveError = "Unable to save message: Chat context not available"
            showSaveAlert = true
            return
        }

        // Convert timestamp string to Date
        let timestamp = convertTimestampToDate(message.timestamp)

        do {
            try await savedMessagesService.saveMessage(
                content: message.text,
                originalMessageId: message.id,
                sourceChatId: currentChatSession.id,
                sourceHeroName: currentChatSession.displayName,
                sourceHeroEmoji: currentChatSession.displayEmoji,
                originalTimestamp: timestamp,
                isFromUser: message.isFromUser
            )

            saveError = nil
            showSaveAlert = true
            
            #if DEBUG
            print("✅ Message saved successfully: \(message.id)")
            #endif
        } catch {
            #if DEBUG
            print("❌ Failed to save message: \(error)")
            #endif
            saveError = error.localizedDescription
            showSaveAlert = true
        }
    }

    private func unsaveMessage() async {
        // Check authentication first
        guard authService.currentUser != nil else {
            saveError = "You must be logged in to manage saved messages"
            showSaveAlert = true
            return
        }
        
        // Find the saved message by original message ID
        guard let savedMessage = savedMessagesService.savedMessages.first(where: { $0.originalMessageId == message.id }) else {
            saveError = "Message not found in saved messages"
            showSaveAlert = true
            return
        }

        do {
            try await savedMessagesService.deleteSavedMessage(savedMessage)
            saveError = nil
            showSaveAlert = true
            
            #if DEBUG
            print("✅ Message unsaved successfully: \(message.id)")
            #endif
        } catch {
            #if DEBUG
            print("❌ Failed to unsave message: \(error)")
            #endif
            saveError = error.localizedDescription
            showSaveAlert = true
        }
    }

    private func convertTimestampToDate(_ timestampString: String) -> Date {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"

        if let time = formatter.date(from: timestampString) {
            // Combine with today's date
            let calendar = Calendar.current
            let today = Date()
            let timeComponents = calendar.dateComponents([.hour, .minute], from: time)

            return calendar.date(bySettingHour: timeComponents.hour ?? 0,
                               minute: timeComponents.minute ?? 0,
                               second: 0,
                               of: today) ?? Date()
        }

        return Date()
    }
}

// MARK: - Message Content
struct MessageContent: View {
    let message: ChatMessage
    @ObservedObject private var savedMessagesService = SavedMessagesService.shared
    @State private var animationScale: CGFloat = 1.0

    var body: some View {
        HStack(alignment: .bottom) {
            ZStack(alignment: message.isFromUser ? .bottomLeading : .bottomTrailing) {
                messageContentView
                    .padding()
                    .multilineTextAlignment(.leading)
                    .background(message.backgroundColor)
                    .clipShape(BubbleTail(messageType: message.isFromUser ? .sent : .received))
                    .frame(maxWidth: UIScreen.main.bounds.width * 0.75,
                           alignment: message.horizontalAlignment)
                    .scaleEffect(animationScale)

                // Bookmark indicator for saved messages
                if isMessageSaved {
                    bookmarkIndicator
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .messageTargeted)) { notification in
            if let targetMessageId = notification.object as? String,
               targetMessageId == message.id {
                performScaleAnimation()
            }
        }
    }

    // MARK: - Computed Properties

    private var isMessageSaved: Bool {
        savedMessagesService.savedMessages.contains { savedMessage in
            savedMessage.originalMessageId == message.id
        }
    }

    private var bookmarkIndicator: some View {
        Image(systemName: "bookmark.fill")
            .font(.caption)
            .foregroundColor(.accentColor)
            .padding(4)
            .background(
                Circle()
                    .fill(.regularMaterial)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            )
            .offset(
                x: message.isFromUser ? 8 : -8,
                y: 8
            )
    }

    // MARK: - Animation Methods

    private func performScaleAnimation() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6, blendDuration: 0)) {
            animationScale = 1.1
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.7, blendDuration: 0)) {
                animationScale = 1.0
            }
        }
    }

    @ViewBuilder
    private var messageContentView: some View {
        switch message.messageType {
        case .text:
            Text(message.text)
                .foregroundColor(message.textColor)
        case .file:
            FileMessageContent(message: message)
        case .image:
            ImageMessageContent(message: message)
        case .date:
            EmptyView()
        }
    }
}

// MARK: - File Message Content
struct FileMessageContent: View {
    let message: ChatMessage
    
    var body: some View {
        HStack(spacing: 12) {
            ZStack {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.accentColor.opacity(0.2))
                    .frame(width: 32, height: 32)

                Image(systemName: "doc.fill")
                    .font(.body)
                    .foregroundColor(.accentColor)
            }
            
            VStack(alignment: .leading, spacing: 2) {
                Text(message.text)
                    .font(.body)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Text("48 KB • PDF")
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
        }
    }
}

// MARK: - Image Message Content
struct ImageMessageContent: View {
    let message: ChatMessage
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 4) {
                imagePreview(color: .green, emoji: "🌿")
                imagePreview(color: .blue, emoji: "🌊")
            }
            
            if !message.text.isEmpty {
                Text(message.text)
                    .font(.body)
            }
        }
    }
    
    private func imagePreview(color: Color, emoji: String) -> some View {
        ZStack {
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.3))
                .frame(width: 60, height: 60)
            
            Text(emoji)
                .font(.title2)
        }
    }
}

// MARK: - Bubble Tail Shape
struct BubbleTail: Shape {
    enum MessageType {
        case sent, received
    }
    
    let messageType: MessageType
    
    func path(in rect: CGRect) -> Path {
        return (messageType == .received) ? getLeftBubblePath(in: rect) : getRightBubblePath(in: rect)
    }
    
    private func getLeftBubblePath(in rect: CGRect) -> Path {
        let width = rect.width
        let height = rect.height
        let path = Path { p in
            p.move(to: CGPoint(x: 25, y: height))
            p.addLine(to: CGPoint(x: width - 20, y: height))
            p.addCurve(to: CGPoint(x: width, y: height - 20),
                       control1: CGPoint(x: width - 8, y: height),
                       control2: CGPoint(x: width, y: height - 8))
            p.addLine(to: CGPoint(x: width, y: 20))
            p.addCurve(to: CGPoint(x: width - 20, y: 0),
                       control1: CGPoint(x: width, y: 8),
                       control2: CGPoint(x: width - 8, y: 0))
            p.addLine(to: CGPoint(x: 21, y: 0))
            p.addCurve(to: CGPoint(x: 4, y: 20),
                       control1: CGPoint(x: 12, y: 0),
                       control2: CGPoint(x: 4, y: 8))
            p.addLine(to: CGPoint(x: 4, y: height - 11))
            p.addCurve(to: CGPoint(x: 0, y: height),
                       control1: CGPoint(x: 4, y: height - 1),
                       control2: CGPoint(x: 0, y: height))
            p.addLine(to: CGPoint(x: -0.05, y: height - 0.01))
            p.addCurve(to: CGPoint(x: 11.0, y: height - 4.0),
                       control1: CGPoint(x: 4.0, y: height + 0.5),
                       control2: CGPoint(x: 8, y: height - 1))
            p.addCurve(to: CGPoint(x: 25, y: height),
                       control1: CGPoint(x: 16, y: height),
                       control2: CGPoint(x: 20, y: height))
        }
        return path
    }
    
    private func getRightBubblePath(in rect: CGRect) -> Path {
        let width = rect.width
        let height = rect.height
        let path = Path { p in
            p.move(to: CGPoint(x: 25, y: height))
            p.addLine(to: CGPoint(x: 20, y: height))
            p.addCurve(to: CGPoint(x: 0, y: height - 20),
                       control1: CGPoint(x: 8, y: height),
                       control2: CGPoint(x: 0, y: height - 8))
            p.addLine(to: CGPoint(x: 0, y: 20))
            p.addCurve(to: CGPoint(x: 20, y: 0),
                       control1: CGPoint(x: 0, y: 8),
                       control2: CGPoint(x: 8, y: 0))
            p.addLine(to: CGPoint(x: width - 21, y: 0))
            p.addCurve(to: CGPoint(x: width - 4, y: 20),
                       control1: CGPoint(x: width - 12, y: 0),
                       control2: CGPoint(x: width - 4, y: 8))
            p.addLine(to: CGPoint(x: width - 4, y: height - 11))
            p.addCurve(to: CGPoint(x: width, y: height),
                       control1: CGPoint(x: width - 4, y: height - 1),
                       control2: CGPoint(x: width, y: height))
            p.addLine(to: CGPoint(x: width + 0.05, y: height - 0.01))
            p.addCurve(to: CGPoint(x: width - 11, y: height - 4),
                       control1: CGPoint(x: width - 4, y: height + 0.5),
                       control2: CGPoint(x: width - 8, y: height - 1))
            p.addCurve(to: CGPoint(x: width - 25, y: height),
                       control1: CGPoint(x: width - 16, y: height),
                       control2: CGPoint(x: width - 20, y: height))
        }
        return path
    }
}

#Preview {
    MessagesScrollView(
        messages: ChatMessage.sampleMessages,
        isLoading: false
    )
}
