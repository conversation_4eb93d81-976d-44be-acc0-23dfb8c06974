//
//  ChatDetailView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ChatDetailView: View {
    let chatSession: ChatSession
    @StateObject private var openAIService = OpenAIService()
    @ObservedObject private var tokenManager = TokenManager.shared
    @ObservedObject private var chatService = ChatService.shared
    @ObservedObject private var authService = AuthenticationService.shared
    @EnvironmentObject private var navigationRouter: NavigationRouter
    @State private var messageText = ""
    @State private var currentChatSession: ChatSession
    @FocusState private var isInputFocused: Bool
    @State private var showSubscriptionAlert = false
    @State private var isStreaming = false
    @State private var streamingResponse = ""
    @State private var highlightedMessageId: String?

    private var hero: HeroPersona {
        currentChatSession.heroPersona ?? .rumi
    }

    init(chatSession: ChatSession) {
        self.chatSession = chatSession
        self._currentChatSession = State(initialValue: chatSess<PERSON>)
    }

    var body: some View {
        VStack(spacing: 0) {
            // Token status header
            tokenStatusHeader

            // Messages scroll view with pagination
            MessagesPaginatedScrollView(
                messages: allMessages,
                chatSession: currentChatSession,
                isLoading: openAIService.isLoading,
                isLoadingMore: chatService.isLoadingMoreMessages,
                hasMoreMessages: chatService.hasMoreMessages,
                showTypingIndicator: openAIService.isLoading || isStreaming,
                isInputFocused: isInputFocused,
                highlightedMessageId: highlightedMessageId,
                onLoadMore: {
                    Task {
                        await loadMoreMessages()
                    }
                }
            )

            // Input bar
            ChatInputBar(
                messageText: $messageText,
                isInputFocused: $isInputFocused,
                onSendMessage: {
                    Task {
                        await sendMessage()
                    }
                },
                onAttachFile: attachFile
            )
            .disabled(tokenManager.isTokenLimitReached && !tokenManager.isSubscribed)
        }
        .toolbarVisibility(.hidden, for: .tabBar)
        .navigationTitle(hero.displayName)
        .navigationBarTitleDisplayMode(.inline)
        .onTapGesture {
            // Hide keyboard when tapping outside input
            isInputFocused = false
        }
        .task {
            await setupChat()
        }
        .onDisappear {
            // Clean up listeners when leaving the chat
            chatService.removeAllListeners()
        }
        .onAppear {
            if let userId = authService.currentUser?.uid {
                chatService.startListeningForNewMessages(userId: userId, chatId: currentChatSession.id)
            }

            // Handle navigation to specific message
            handleMessageNavigation()
        }
        .onDisappear {
            chatService.removeAllListeners()
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                heroAvatar
            }
        }
        .alert("Token Limit Reached", isPresented: $showSubscriptionAlert) {
            Button("Subscribe") {
                Task {
                    if let userId = authService.currentUser?.uid {
                        await tokenManager.setSubscriptionStatus(userId: userId, isSubscribed: true)
                    }
                }
            }
            Button("Cancel", role: .cancel) {}
        } message: {
            Text("You've used all your free tokens. Subscribe to continue chatting with unlimited access.")
        }
    }

    // MARK: - Private Methods

    private func handleMessageNavigation() {
        guard navigationRouter.shouldScrollToMessage,
              let targetMessageId = navigationRouter.targetMessageId else {
            return
        }

        // Clear navigation target immediately
        navigationRouter.clearMessageTarget()

        #if DEBUG
        print("🎯 ChatDetailView: Navigation to message \(targetMessageId) handled - animation will be triggered by NavigationRouter")
        #endif
    }

    // MARK: - Private Views
    private var tokenStatusHeader: some View {
        HStack {
            if !tokenManager.isSubscribed {
                HStack(spacing: 4) {
                    Image(systemName: "bolt.fill")
                        .foregroundColor(.orange)
                        .font(.caption)

                    Text("\(tokenManager.getActualRemainingTokens()) tokens remaining")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .animation(.easeInOut(duration: 0.3), value: tokenManager.remainingTokens)
                }
            } else {
                HStack(spacing: 4) {
                    Image(systemName: "infinity")
                        .foregroundColor(.green)
                        .font(.caption)

                    Text("Unlimited")
                        .font(.caption)
                        .foregroundColor(.green)
                }
            }

            Spacer()
        }
        .padding(.horizontal, AppConstants.UI.padding)
        .padding(.top, 8)
    }

    private var heroAvatar: some View {
        ZStack {
            Circle()
                .fill(.orange.gradient)
                .frame(width: 32, height: 32)

            Text(hero.emoji)
                .font(.system(size: 16))
        }
    }

    private var allMessages: [ChatMessage] {
        var allMsgs = convertFirestoreMessagesToChatMessages(chatService.currentChatMessages)

        // Add streaming response if active - ensure it has a consistent ID
        if isStreaming && !streamingResponse.isEmpty {
            let streamingMsg = ChatMessage(
                id: "streaming-response", // Consistent ID to prevent animation glitches
                text: streamingResponse,
                isFromUser: false,
                timestamp: getCurrentTimestamp()
            )
            allMsgs.append(streamingMsg)
        }

        return allMsgs
    }

    // MARK: - Private Methods
    private func setupChat() async {
        guard let userId = authService.currentUser?.uid else {
            print("❌ ChatDetailView: Cannot setup chat - user not authenticated")
            return
        }

        // Clear any existing listeners and messages
        chatService.removeAllListeners()
        chatService.clearCurrentMessages()

        // Configure SavedMessagesService for the authenticated user
        SavedMessagesService.shared.configure(for: userId)

        await tokenManager.loadUserTokens(userId: userId)

        // Load initial messages from Firebase
        await chatService.loadChatMessages(userId: userId, chatId: currentChatSession.id, isInitialLoad: true)

        // Start listening for new messages in real-time
        chatService.startListeningForNewMessages(userId: userId, chatId: currentChatSession.id)

        // Sync with chat service to get latest state
        if let latestChatSession = chatService.getChatSession(chatId: currentChatSession.id) {
            currentChatSession = latestChatSession
        }

        #if DEBUG
        print("✅ ChatDetailView: Chat setup completed for chat \(currentChatSession.id)")
        #endif
    }
    
    private func loadMoreMessages() async {
        guard let userId = authService.currentUser?.uid else { return }
        await chatService.loadChatMessages(userId: userId, chatId: currentChatSession.id, isInitialLoad: false)
    }
    
    private func convertFirestoreMessagesToChatMessages(_ firestoreMessages: [FirestoreChatMessage]) -> [ChatMessage] {
        return firestoreMessages.compactMap { firestoreMessage in
            // Skip system messages in UI
            guard firestoreMessage.role != "system" else { return nil }

            return ChatMessage(
                id: firestoreMessage.id,  // Preserve original message ID for saving
                text: firestoreMessage.content,
                isFromUser: firestoreMessage.role == "user",
                timestamp: getCurrentTimestamp(from: firestoreMessage.timestamp)
            )
        }
    }

    private func sendMessage() async {
        guard let userId = authService.currentUser?.uid else { return }
        guard !messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        guard tokenManager.canSendMessage() else {
            showSubscriptionAlert = true
            return
        }

        let userMessage = ChatMessage(
            text: messageText,
            isFromUser: true,
            timestamp: getCurrentTimestamp()
        )

        let messageToSend = messageText
        messageText = ""

        // Hide keyboard when sending message
        isInputFocused = false

        // Optimistically consume estimated tokens for immediate UI feedback
        let estimatedTokens = estimateTokens(for: messageToSend)
        tokenManager.optimisticallyConsumeTokens(tokensUsed: estimatedTokens)

        // Check if this is the first message - if so, save chat to Firebase first
        let isFirstMessage = chatService.currentChatMessages.isEmpty
        if isFirstMessage {
            await chatService.saveNewChatToFirebase(userId: userId, chatSession: currentChatSession)
        }

        // Add user message to chat service (Firebase listener will handle UI updates)
        await chatService.addMessage(
            userId: userId,
            chatId: currentChatSession.id,
            message: userMessage,
            tokensUsed: 0
        )

        // Send to OpenAI with conversation context
        if AppConfiguration.Features.enableStreaming {
            await sendStreamingMessage(messageToSend: messageToSend, userId: userId)
        } else {
            await sendRegularMessage(messageToSend: messageToSend, userId: userId)
        }
    }

    private func sendRegularMessage(messageToSend: String, userId: String) async {
        do {
            // Use all messages from chat service for context
            let contextMessages = convertFirestoreMessagesToChatMessages(chatService.currentChatMessages)
            
            // Send the complete conversation history to OpenAI
            let aiMessage = try await openAIService.sendMessage(
                messages: contextMessages,
                hero: hero
            )

            // Add AI response to chat service (which will save to Firebase)
            let tokensUsed = estimateTokens(for: messageToSend + aiMessage.text)
            await chatService.addMessage(
                userId: userId,
                chatId: currentChatSession.id,
                message: aiMessage,
                tokensUsed: tokensUsed
            )

            // Update token usage
            await tokenManager.consumeTokens(
                userId: userId,
                tokensUsed: tokensUsed
            )

        } catch {
            print("❌ ChatDetailView: OpenAI Error: \(error)")
            openAIService.error = error.localizedDescription

            // Create error message for user
            let errorMessage = ChatMessage(
                text: "Sorry, I encountered an error: \(error.localizedDescription). Please try again.",
                isFromUser: false,
                timestamp: getCurrentTimestamp()
            )

            // Add error message to chat
            await chatService.addMessage(
                userId: userId,
                chatId: currentChatSession.id,
                message: errorMessage,
                tokensUsed: 0
            )
        }
    }

    private func sendStreamingMessage(messageToSend: String, userId: String) async {
        // Start streaming with smooth transition
        await MainActor.run {
            withAnimation(.easeInOut(duration: 0.2)) {
                isStreaming = true
                streamingResponse = ""
            }
        }

        do {
            // Use all messages from chat service for context
            let contextMessages = convertFirestoreMessagesToChatMessages(chatService.currentChatMessages)

            let stream = openAIService.sendMessageStream(
                messages: contextMessages,
                hero: hero
            )

            for try await response in stream {
                await MainActor.run {
                    streamingResponse = response
                }
            }

            // Create final AI message
            let aiMessage = ChatMessage(
                text: streamingResponse,
                isFromUser: false,
                timestamp: getCurrentTimestamp()
            )

            // Stop streaming before adding final message to prevent duplication
            await MainActor.run {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isStreaming = false
                    streamingResponse = ""
                }
            }

            // Add AI message to chat service (which will save to Firebase)
            let tokensUsed = estimateTokens(for: messageToSend + aiMessage.text)
            await chatService.addMessage(
                userId: userId,
                chatId: currentChatSession.id,
                message: aiMessage,
                tokensUsed: tokensUsed
            )

            // Update token usage
            await tokenManager.consumeTokens(
                userId: userId,
                tokensUsed: tokensUsed
            )

            // End streaming with smooth transition
            await MainActor.run {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isStreaming = false
                    streamingResponse = ""
                }
            }

        } catch {
            print("❌ ChatDetailView: Streaming error: \(error)")
            openAIService.error = error.localizedDescription

            // Create error message for user
            let errorMessage = ChatMessage(
                text: "Sorry, I encountered an error during streaming: \(error.localizedDescription). Please try again.",
                isFromUser: false,
                timestamp: getCurrentTimestamp()
            )

            // Add error message to chat
            await chatService.addMessage(
                userId: userId,
                chatId: currentChatSession.id,
                message: errorMessage,
                tokensUsed: 0
            )

            // End streaming with smooth transition
            await MainActor.run {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isStreaming = false
                    streamingResponse = ""
                }
            }
        }
    }

    private func attachFile() {
        // TODO: Implement file attachment
        print("Attach file tapped")
    }

    private func getCurrentTimestamp() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: Date())
    }

    private func getCurrentTimestamp(from date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }

    private func estimateTokens(for text: String) -> Int {
        // Rough estimation: 1 token ≈ 4 characters
        return max(1, text.count / 4)
    }
}

// MARK: - Messages Paginated Scroll View
struct MessagesPaginatedScrollView: View {
    let messages: [ChatMessage]
    let chatSession: ChatSession
    let isLoading: Bool
    let isLoadingMore: Bool
    let hasMoreMessages: Bool
    let showTypingIndicator: Bool
    let isInputFocused: Bool
    let highlightedMessageId: String?
    let onLoadMore: () -> Void

    @State private var isScrollingToBottom = false
    @State private var lastMessageCount = 0

    var body: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 6) {
                    // Load more indicator at top
                    if hasMoreMessages {
                        HStack {
                            if isLoadingMore {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text("Loading older messages...")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            } else {
                                Button("Load older messages") {
                                    onLoadMore()
                                }
                                .font(.caption)
                                .foregroundColor(.accentColor)
                            }
                        }
                        .padding()
                        .frame(maxWidth: .infinity)
                    }

                    // Messages
                    ForEach(Array(messages.enumerated()), id: \.element.id) { index, message in
                        MessageBubbleView(
                            message: message,
                            chatSession: chatSession,
                            isHighlighted: highlightedMessageId == message.id
                        )
                        .id(message.id)
                    }

                    // Typing indicator positioned after messages
                    if showTypingIndicator {
                        HStack {
                            TypingIndicatorView()
                            Spacer()
                        }
                        .padding(.top, 8)
                        .transition(.opacity.combined(with: .move(edge: .bottom)))
                        .id("typing-indicator")
                    }
                }
                .padding(.horizontal, AppConstants.UI.padding)
            }
            .scrollDismissesKeyboard(.interactively)
            .onAppear {
                // Scroll to bottom on first load without animation
                if let lastMessage = messages.last {
                    proxy.scrollTo(lastMessage.id, anchor: .bottom)
                }
                lastMessageCount = messages.count
            }
            .onChange(of: messages.count) { oldValue, newValue in
                // Only auto-scroll if messages were added (not removed or reordered)
                guard newValue > oldValue, !isScrollingToBottom else { return }
                guard let lastMessage = messages.last else { return }

                isScrollingToBottom = true

                withAnimation(.easeOut(duration: 0.4)) {
                    proxy.scrollTo(lastMessage.id, anchor: .bottom)
                }

                // Reset flag after animation completes using animation completion
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
                    isScrollingToBottom = false
                }
            }
            .onChange(of: showTypingIndicator) { _, isShowing in
                guard isShowing, !isScrollingToBottom else { return }

                isScrollingToBottom = true

                withAnimation(.easeOut(duration: 0.3)) {
                    proxy.scrollTo("typing-indicator", anchor: .bottom)
                }

                // Reset flag after animation completes
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    isScrollingToBottom = false
                }
            }
            .onChange(of: isInputFocused) { _, isFocused in
                guard isFocused, !isScrollingToBottom, let lastMessage = messages.last else { return }

                // Use keyboard notification timing instead of hardcoded delay
                isScrollingToBottom = true

                withAnimation(.easeOut(duration: 0.5)) {
                    proxy.scrollTo(lastMessage.id, anchor: .bottom)
                }

                // Reset flag after animation completes
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    isScrollingToBottom = false
                }
            }
        }
    }
}

// MARK: - Updated ChatDetailView Initializer
extension ChatDetailView {
    // Keep compatibility with old ChatItem
    init(chat: ChatItem) {
        let session = ChatSession(
            id: UUID().uuidString,
            hero: HeroPersona.rumi.rawValue,
            createdAt: Date(),
            messages: [],
            isFavorite: false,
            category: nil,
            tokensUsed: 0,
            lastMessageTimestamp: Date().timeIntervalSince1970
        )
        self.init(chatSession: session)
    }
}

#Preview {
    NavigationStack {
        ChatDetailView(chatSession: ChatSession(
            id: "preview",
            hero: HeroPersona.rumi.rawValue,
            createdAt: Date(),
            messages: [],
            isFavorite: false,
            category: nil,
            tokensUsed: 0,
            lastMessageTimestamp: Date().timeIntervalSince1970
        ))
    }
}
