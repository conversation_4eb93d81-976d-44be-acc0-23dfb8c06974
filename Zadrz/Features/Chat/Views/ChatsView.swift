//
//  ChatsView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ChatsView: View {
    @ObservedObject private var chatService = ChatService.shared
    @ObservedObject private var authService = AuthenticationService.shared
    @State private var searchText: String = ""
    @State private var selectedFilter: ChatFilter = .all
    @State private var chatToDelete: ChatSession?
    @State private var showDeleteConfirmation = false

    var body: some View {
        NavigationStack {
            VStack {
                if chatService.isLoading {
                    loadingView
                } else if chatService.isEmpty && searchText.isEmpty {
                    emptyStateView
                } else {
                    chatListView
                }
            }
            .navigationTitle("Chats")
            .searchable(text: $searchText, prompt: "Search chats...")
            .refreshable {
                if let userId = authService.currentUser?.uid {
                    await chatService.loadChats(userId: userId)
                }
            }
            .toolbar {
                trailingNavItems()
            }
            .task {
                if let userId = authService.currentUser?.uid {
                    await chatService.loadChats(userId: userId)
                    // Start listening for real-time chat updates
                    chatService.startListeningForChatUpdates(userId: userId)
                }
            }
            .onDisappear {
                chatService.removeAllListeners()
            }
            .alert("Delete Chat", isPresented: $showDeleteConfirmation) {
                Button("Cancel", role: .cancel) { }
                Button("Delete", role: .destructive) {
                    if let chat = chatToDelete,
                       let userId = authService.currentUser?.uid {
                        Task {
                            await chatService.deleteChat(userId: userId, chatId: chat.id)
                        }
                    }
                    chatToDelete = nil
                }
            } message: {
                Text("Are you sure you want to delete this chat? This action cannot be undone.")
            }
        }
    }

    // MARK: - Private Views
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Loading chats...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            VStack(spacing: 16) {
                Image(systemName: "bubble.left.and.bubble.right")
                    .font(.system(size: 64))
                    .foregroundColor(.secondary)
                
                VStack(spacing: 8) {
                    Text("No Messages Yet")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("Start a conversation with one of our wise guides")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            
        }
        .padding(AppConstants.UI.padding)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var chatListView: some View {
        List {
            // Category filters section
            if !chatService.chats.isEmpty {
                categoryFiltersSection()
                    .listRowSeparator(.hidden, edges: .top)
            }
            
            // Chat list
            ForEach(filteredChats) { chat in
                NavigationLink(value: chat) {
                    ChatSessionRowView(chatSession: chat)
                }
                .swipeActions(edge: .leading, allowsFullSwipe: true) {
                    Button {
                        Task {
                            guard let userId = authService.currentUser?.uid else { return }
                            await chatService.toggleChatFavorite(userId: userId, chatId: chat.id)
                        }
                    } label: {
                        Label(
                            chat.isFavorite ? "Unfavorite" : "Favorite",
                            systemImage: chat.isFavorite ? "star.slash.fill" : "star.fill"
                        )
                    }
                    .tint(chat.isFavorite ? .orange : .yellow)
                }
            }
            .onDelete { indexSet in
                if let index = indexSet.first {
                    chatToDelete = filteredChats[index]
                    showDeleteConfirmation = true
                }
            }
            
            // Footer section if not empty
            if !chatService.chats.isEmpty {
                inboxFooterView()
                    .listRowSeparator(.hidden, edges: .bottom)
            }
        }
        .listStyle(.plain)
        .navigationDestination(for: ChatSession.self) { chatSession in
            LazyView(ChatDetailView(chatSession: chatSession))
        }
    }
    
    private func categoryFiltersSection() -> some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(ChatFilter.allCases, id: \.self) { filter in
                    Button(action: {
                        selectedFilter = filter
                    }) {
                        Text(filter.title)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                selectedFilter == filter
                                ? Color.accentColor
                                : Color.gray.opacity(0.2)
                            )
                            .foregroundColor(
                                selectedFilter == filter
                                ? .white
                                : .primary
                            )
                            .clipShape(Capsule())
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
        }
    }
    
    private func inboxFooterView() -> some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "lock.shield")
                    .font(.title3)
                    .foregroundColor(.secondary)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Your conversations are private and secure")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("Learn more")
                        .font(.subheadline)
                        .foregroundColor(.accentColor)
                }
                
                Spacer()
            }
            .padding(.vertical, 12)
        }
    }
    
    @ToolbarContentBuilder
    private func trailingNavItems() -> some ToolbarContent {
        ToolbarItemGroup(placement: .navigationBarTrailing) {
            Button(action: {
                // TODO: Navigate to new chat / hero selection
            }) {
                Image(systemName: "square.and.pencil")
                    .font(.title3)
            }
        }
    }

    // MARK: - Computed Properties
    private var filteredChats: [ChatSession] {
        let searchFiltered = searchText.isEmpty ? chatService.chats : chatService.chats.filter { chat in
            chat.displayName.localizedCaseInsensitiveContains(searchText) ||
            chat.displayLastMessage.localizedCaseInsensitiveContains(searchText)
        }
        
        // Apply category filter
        switch selectedFilter {
        case .all:
            return searchFiltered
        case .favorites:
            return searchFiltered.filter { $0.isFavorite }
        case .saved:
            return searchFiltered.filter { $0.category != nil }
        }
    }
}

// MARK: - Chat Filter
enum ChatFilter: String, CaseIterable {
    case all = "All Chats"
    case favorites = "Favorites"
    case saved = "Saved"
    
    var title: String {
        self.rawValue
    }
}

#Preview {
    ChatsView()
}
