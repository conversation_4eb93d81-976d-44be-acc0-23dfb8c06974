//
//  ChatsView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ChatsView: View {
    @ObservedObject private var chatService = ChatService.shared
    @ObservedObject private var authService = AuthenticationService.shared
    @State private var searchText: String = ""
    @State private var selectedFilter: ChatFilter = .all
    @State private var chatToDelete: ChatSession?
    @State private var showDeleteConfirmation = false
    @State private var hasLoadedInitially = false
    @State private var isEditMode = false
    @State private var showSavedMessages = false
    @State private var refreshTrigger = UUID()

    var body: some View {
        NavigationStack {
            VStack {
                if authService.currentUser == nil {
                    // Show message for unauthenticated users
                    VStack(spacing: 16) {
                        Image(systemName: "lock.shield")
                            .font(.system(size: 48))
                            .foregroundColor(.secondary)

                        Text("Please log in to view your chats")
                            .font(.title3)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if chatService.isLoading && !hasLoadedInitially {
                    // Show loading only on initial load
                    loadingView
                } else if chatService.chats.isEmpty && searchText.isEmpty && hasLoadedInitially {
                    // Show empty state only after we've tried loading and found no chats
                    emptyStateView
                } else {
                    // Show chat list if authenticated and we have data or have loaded initially
                    chatListView
                        .id(refreshTrigger) // Force refresh when needed
                }
            }
            .navigationTitle("Chats")
            .searchable(text: $searchText, prompt: "Search chats...")
            .refreshable {
                if let userId = authService.currentUser?.uid {
                    await chatService.loadChats(userId: userId)
                }
            }
            .toolbar {
                trailingNavItems()
            }
            .task {
                await loadChatsIfAuthenticated()
            }
            .onChange(of: authService.currentUser?.uid) { oldUserId, newUserId in
                // Reset state when user changes
                if oldUserId != newUserId {
                    hasLoadedInitially = false

                    // Clear existing data when user changes
                    if newUserId == nil {
                        chatService.clearAllData()
                        SavedMessagesService.shared.cleanup()
                    } else {
                        // User logged in or switched - force refresh
                        refreshTrigger = UUID()
                    }

                    // Reload chats when authentication state changes
                    Task {
                        await loadChatsIfAuthenticated()
                    }
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
                // Reload chats when app comes to foreground
                Task {
                    await loadChatsIfAuthenticated()
                }
            }
            .onAppear {
                // If we already have user ID and haven't loaded initially, start loading
                if authService.currentUser?.uid != nil && !hasLoadedInitially {
                    Task {
                        await loadChatsIfAuthenticated()
                    }
                }
            }
            .onDisappear {
                chatService.removeAllListeners()
            }
            .alert("Delete Chat", isPresented: $showDeleteConfirmation) {
                Button("Cancel", role: .cancel) { }
                Button("Delete", role: .destructive) {
                    if let chat = chatToDelete,
                       let userId = authService.currentUser?.uid {
                        Task {
                            await chatService.deleteChat(userId: userId, chatId: chat.id)
                        }
                    }
                    chatToDelete = nil
                }
            } message: {
                Text("Are you sure you want to delete this chat? This action cannot be undone.")
            }
        }
    }

    // MARK: - Private Views
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Loading chats...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            VStack(spacing: 16) {
                Image(systemName: "bubble.left.and.bubble.right")
                    .font(.system(size: 64))
                    .foregroundColor(.secondary)
                
                VStack(spacing: 8) {
                    Text("No Messages Yet")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("Start a conversation with one of our wise guides")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            
        }
        .padding(AppConstants.UI.padding)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var chatListView: some View {
        List {
            // Category filters section
            if !chatService.chats.isEmpty {
                categoryFiltersSection()
                    .listRowSeparator(.hidden, edges: .top)
            }
            
            // Chat list
            ForEach(filteredChats) { chat in
                NavigationLink(value: chat) {
                    ChatSessionRowView(chatSession: chat)
                }
                .swipeActions(edge: .leading, allowsFullSwipe: true) {
                    Button {
                        Task {
                            guard let userId = authService.currentUser?.uid else { return }
                            await chatService.toggleChatFavorite(userId: userId, chatId: chat.id)
                            // Force UI refresh after favorite toggle
                            await MainActor.run {
                                refreshTrigger = UUID()
                            }
                        }
                    } label: {
                        Label(
                            chat.isFavorite ? "Unfavorite" : "Favorite",
                            systemImage: chat.isFavorite ? "star.slash.fill" : "star.fill"
                        )
                    }
                    .tint(chat.isFavorite ? .orange : .yellow)
                }
                .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                    Button {
                        chatToDelete = chat
                        showDeleteConfirmation = true
                    } label: {
                        Label("Delete", systemImage: "trash.fill")
                    }
                    .tint(.red)
                }
            }
            .onDelete(perform: isEditMode ? deleteChats : nil)
            
            // Footer section if not empty
            if !chatService.chats.isEmpty {
                inboxFooterView()
                    .listRowSeparator(.hidden, edges: .bottom)
            }
        }
        .listStyle(.plain)
        .environment(\.editMode, isEditMode ? .constant(.active) : .constant(.inactive))
        .navigationDestination(for: ChatSession.self) { chatSession in
            LazyView(ChatDetailView(chatSession: chatSession))
        }
        .sheet(isPresented: $showSavedMessages) {
            SavedMessagesView()
        }
    }
    
    private func categoryFiltersSection() -> some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(ChatFilter.allCases, id: \.self) { filter in
                    Button(action: {
                        if filter == .saved {
                            showSavedMessages = true
                        } else {
                            selectedFilter = filter
                        }
                    }) {
                        Text(filter.title)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                selectedFilter == filter
                                ? Color.accentColor
                                : Color.gray.opacity(0.2)
                            )
                            .foregroundColor(
                                selectedFilter == filter
                                ? .white
                                : .primary
                            )
                            .clipShape(Capsule())
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
        }
    }
    
    private func inboxFooterView() -> some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "lock.shield")
                    .font(.title3)
                    .foregroundColor(.secondary)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Your conversations are private and secure")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("Learn more")
                        .font(.subheadline)
                        .foregroundColor(.accentColor)
                }
                
                Spacer()
            }
            .padding(.vertical, 12)
        }
    }
    
    @ToolbarContentBuilder
    private func trailingNavItems() -> some ToolbarContent {
        ToolbarItemGroup(placement: .navigationBarTrailing) {
            if !chatService.chats.isEmpty {
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isEditMode.toggle()
                    }
                }) {
                    Text(isEditMode ? "Done" : "Edit")
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.accentColor)
                }
            }
        }
    }

    // MARK: - Computed Properties
    private var filteredChats: [ChatSession] {
        let searchFiltered = searchText.isEmpty ? chatService.chats : chatService.chats.filter { chat in
            chat.displayName.localizedCaseInsensitiveContains(searchText) ||
            chat.displayLastMessage.localizedCaseInsensitiveContains(searchText)
        }

        // Apply category filter
        switch selectedFilter {
        case .all:
            return searchFiltered
        case .favorites:
            return searchFiltered.filter { $0.isFavorite }
        case .saved:
            // Saved filter now opens SavedMessagesView, so this shouldn't be reached
            return searchFiltered
        }
    }

    // MARK: - Private Methods
    private func loadChatsIfAuthenticated() async {
        guard let userId = authService.currentUser?.uid else {
            #if DEBUG
            print("🔄 ChatsView: No authenticated user, skipping chat load")
            #endif
            await MainActor.run {
                hasLoadedInitially = true
            }
            return
        }

        #if DEBUG
        print("🔄 ChatsView: Loading chats for authenticated user: \(userId)")
        #endif

        await chatService.loadChats(userId: userId)
        
        // Start listening for real-time chat updates
        chatService.startListeningForChatUpdates(userId: userId)

        // Configure SavedMessagesService for the authenticated user
        SavedMessagesService.shared.configure(for: userId)

        await MainActor.run {
            hasLoadedInitially = true
            // Force UI update after loading
            refreshTrigger = UUID()
        }
        
        #if DEBUG
        print("✅ ChatsView: Initial load completed, hasLoadedInitially: \(hasLoadedInitially), chat count: \(chatService.chats.count)")
        #endif
    }

    private func deleteChats(at offsets: IndexSet) {
        for index in offsets {
            let chat = filteredChats[index]
            chatToDelete = chat
            showDeleteConfirmation = true
        }
    }
}

// MARK: - Chat Filter
enum ChatFilter: String, CaseIterable {
    case all = "All Chats"
    case favorites = "Favorites"
    case saved = "Saved Messages"

    var title: String {
        self.rawValue
    }
}

#Preview {
    ChatsView()
}
