//
//  ChatRowView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ChatRowView: View {
    let chat: ChatItem
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            avatarView
            contentView
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Private Views
    private var avatarView: some View {
        ZStack {
            Circle()
                .fill(.regularMaterial)
                .frame(width: 60, height: 60)
            
            // TODO: Replace with actual user avatar when available
            Text(chat.name.prefix(1).uppercased())
                .font(.title)
                .fontWeight(.semibold)
                .foregroundStyle(.primary)
        }
    }
    
    private var contentView: some View {
        VStack(alignment: .leading, spacing: 4) {
            headerRow
            messageRow
        }
    }
    
    private var headerRow: some View {
        HStack {
            Text(chat.name)
                .font(.body)
                .fontWeight(.semibold)
                .foregroundStyle(.primary)
                .lineLimit(1)
            
            Spacer()
            
            Text(chat.time)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
    
    private var messageRow: some View {
        HStack(alignment: .bottom) {
            Text(chat.message)
                .font(.subheadline)
                .foregroundStyle(.secondary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            Spacer()
            
            if chat.unreadCount > 0 {
                unreadBadge
            }
        }
    }
    
    private var unreadBadge: some View {
        Text("\(chat.unreadCount)")
            .font(.caption2)
            .fontWeight(.bold)
            .foregroundColor(.white)
            .frame(minWidth: 20, minHeight: 20)
            .background(
                Circle()
                    .fill(Color.accentColor)
            )
    }
}

// MARK: - Chat Session Row View
struct ChatSessionRowView: View {
    let chatSession: ChatSession
    
    var body: some View {
        HStack(spacing: 12) {
            // Hero avatar
            ZStack {
                Circle()
                    .fill(Color.accentColor.gradient)
                    .frame(width: 48, height: 48)
                
                Text(chatSession.displayEmoji)
                    .font(.title2)
            }
            
            // Chat info
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(chatSession.displayName)
                        .font(.headline)
                        .fontWeight(.medium)

                    if chatSession.isFavorite {
                        Image(systemName: "star.fill")
                            .font(.caption)
                            .foregroundColor(.accentColor)
                    }

                    Spacer()

                    Text(chatSession.lastMessageTime)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Text(chatSession.displayLastMessage)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            if chatSession.tokensUsed > 0 {
                VStack {
                    Spacer()
                    Text("\(chatSession.tokensUsed)")
                        .font(.caption2)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(.orange.opacity(0.2))
                        .foregroundColor(.orange)
                        .clipShape(Capsule())
                }
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    List {
        ForEach(ChatItem.sampleChats.prefix(3), id: \.id) { chat in
            ChatRowView(chat: chat)
        }
    }
    .listStyle(.plain)
}
