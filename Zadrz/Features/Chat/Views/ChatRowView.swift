//
//  ChatRowView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

// MARK: - Chat Session Row View
/// Modern chat row view using ChatSession from ChatService
struct ChatSessionRowView: View {
    let chatSession: ChatSession

    var body: some View {
        HStack(spacing: 12) {
            // Hero avatar using reusable AvatarView
            AvatarView.emoji(chatSession.displayEmoji, size: 48)

            // Chat info
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(chatSession.displayName)
                        .font(.headline)
                        .fontWeight(.medium)

                    if chatSession.isFavorite {
                        Image(systemName: "star.fill")
                            .font(.caption)
                            .foregroundColor(.accentColor)
                    }

                    Spacer()

                    Text(chatSession.lastMessageTime)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Text(chatSession.displayLastMessage)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            if chatSession.tokensUsed > 0 {
                TokenBadge(count: chatSession.tokensUsed)
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Token Badge Component
private struct TokenBadge: View {
    let count: Int

    var body: some View {
        VStack {
            Spacer()
            Text("\(count)")
                .font(.caption2)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(.orange.opacity(0.2))
                .foregroundColor(.orange)
                .clipShape(Capsule())
        }
    }
}

// MARK: - Legacy Chat Row View
/// Legacy view for ChatItem model - kept for backward compatibility
/// @deprecated Use ChatSessionRowView instead
struct ChatRowView: View {
    let chat: ChatItem

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // Use reusable AvatarView component
            AvatarView.initials(chat.name, size: 60)

            contentView
        }
        .padding(.vertical, 8)
    }

    // MARK: - Private Views
    private var contentView: some View {
        VStack(alignment: .leading, spacing: 4) {
            headerRow
            messageRow
        }
    }

    private var headerRow: some View {
        HStack {
            Text(chat.name)
                .font(.body)
                .fontWeight(.semibold)
                .foregroundStyle(.primary)
                .lineLimit(1)

            Spacer()

            Text(chat.time)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }

    private var messageRow: some View {
        HStack(alignment: .bottom) {
            Text(chat.message)
                .font(.subheadline)
                .foregroundStyle(.secondary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)

            Spacer()

            if chat.unreadCount > 0 {
                UnreadBadge(count: chat.unreadCount)
            }
        }
    }
}

// MARK: - Unread Badge Component
private struct UnreadBadge: View {
    let count: Int

    var body: some View {
        Text("\(count)")
            .font(.caption2)
            .fontWeight(.bold)
            .foregroundColor(.white)
            .frame(minWidth: 20, minHeight: 20)
            .background(
                Circle()
                    .fill(Color.accentColor)
            )
    }
}
