//
//  InterestsSelectionView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct InterestsSelectionView: View {
    private let interests = [
        "Astrophysics", "Basketball", "Ancient Civilizations", "Modern Poetry",
        "Quantum Mechanics", "Biology", "Chemistry", "Environmental Science",
        "Geology", "Physics", "Sociology", "Astronomy", "Philosophy",
        "Anthropology", "Statistics", "Marine Biology"
    ]
    
    var body: some View {
        SelectionView(
            title: "Pick your interests",
            options: interests,
            allowMultipleSelection: true
        ) { selectedInterests in
            handleInterestSelection(selectedInterests)
        }
        .navigationTitle("Interests")
        .navigationDestination(for: String.self) { destination in
            if destination == "conversationStyle" {
                ConversationStyleView()
            }
        }
    }
    
    private func handleInterestSelection(_ interests: [String]) {
        print("Selected interests: \(interests)")
        // This will be handled by the SelectionView's navigation
    }
}

#Preview {
    NavigationStack {
        InterestsSelectionView()
    }
}
