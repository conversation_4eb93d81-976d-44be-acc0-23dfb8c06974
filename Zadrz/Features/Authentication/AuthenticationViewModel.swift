//
//  AuthenticationViewModel.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation
import Combine

/// View model for handling authentication operations
/// Manages user authentication state, form validation, and UI feedback
/// Uses the AuthenticationService for actual authentication operations
@MainActor
final class AuthenticationViewModel: ObservableObject {

    // MARK: - Published Properties
    /// Current authentication status
    @Published var isAuthenticated = false

    /// Currently authenticated user data
    @Published var currentUser: UserModel?

    /// General loading state
    @Published var isLoading = false

    /// Current error message to display
    @Published var errorMessage: String?

    /// Current success message to display
    @Published var successMessage: String?

    /// Controls success alert visibility
    @Published var showSuccessAlert = false

    /// Controls error alert visibility
    @Published var showErrorAlert = false
    
    // MARK: - Form Fields
    @Published var email: String = ""
    @Published var fullName: String = ""
    @Published var password: String = ""
    @Published var phoneNumber: String = ""
    @Published var currentPassword: String = ""
    @Published var newEmail: String = ""
    @Published var newPassword: String = ""
    @Published var passwordConfirmation: String = ""
    
    // MARK: - Loading States
    @Published var isGoogleSignInLoading = false
    @Published var isAppleSignInLoading = false
    @Published var isEmailSignInLoading = false
    @Published var isSignUpLoading = false
    
    // MARK: - Feedback Triggers
    @Published var successTrigger = false
    @Published var errorTrigger = false
    @Published var didLogout = false
    
    // MARK: - Private Properties
    private let _authService: AuthenticationServiceProtocol
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Public Properties
    var authService: AuthenticationServiceProtocol {
        return _authService
    }
    
    // MARK: - Initialization
    init(authService: AuthenticationServiceProtocol = AuthenticationService.shared) {
        self._authService = authService
        setupAuthStateListener()
    }
    
    // MARK: - Public Methods
    func tryAutoLogin() async {
        await _authService.autoLogin()
    }
    
    func signUp() async {
        guard validateSignUpForm() else { return }
        
        isSignUpLoading = true
        isLoading = true
        clearErrorState()
        
        do {
            try await _authService.createAccount(
                email: email,
                fullName: fullName,
                password: password
            )
            
            // Only haptic feedback for sign up success, no alert
            triggerSuccessFeedback()
            clearSignUpFields()
        } catch {
            showError("Failed to create account: \(error.localizedDescription)")
        }
        
        isSignUpLoading = false
        isLoading = false
    }
    
    func signIn() async -> Bool {
        guard validateSignInForm() else { return false }
        
        isEmailSignInLoading = true
        clearErrorState()
        
        do {
            try await _authService.login(email: email, password: password)
            // Only haptic feedback for sign in success, no alert
            triggerSuccessFeedback()
            clearSignInFields()
            isEmailSignInLoading = false
            return true
        } catch {
            showError("Failed to sign in: \(error.localizedDescription)")
            isEmailSignInLoading = false
            return false
        }
    }
    
    func logout() async {
        isLoading = true
        clearErrorState()
        
        do {
            try await _authService.logout()
            didLogout = true
            // Only haptic feedback for logout, no success message
            triggerSuccessFeedback()
            // Clear all form fields on logout
            clearAllFields()
        } catch {
            showError("Failed to logout: \(error.localizedDescription)")
        }
        
        isLoading = false
    }
    
    func resetPassword() async {
        guard !email.isEmpty, email.isValidEmail else {
            showError("Please enter a valid email address.")
            return
        }
        
        isLoading = true
        clearErrorState()
        
        do {
            try await _authService.resetPassword(email: email)
            showSuccess("Password reset email sent successfully.")
        } catch {
            showError("Failed to send reset email: \(error.localizedDescription)")
        }
        
        isLoading = false
    }

    func loadCurrentUser() async {
        do {
            currentUser = try await _authService.getCurrentUserData()
        } catch {
            showError("Failed to load user data: \(error.localizedDescription)")
        }
    }

    // MARK: - Public Methods
    func triggerSuccessFeedback() {
        successTrigger.toggle()
    }
    
    // MARK: - Private Methods
    private func setupAuthStateListener() {
        _authService.authState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                switch state {
                case .authenticated:
                    self?.isAuthenticated = true
                    self?.didLogout = false
                    Task {
                        await self?.loadCurrentUser()
                    }
                case .unauthenticated:
                    self?.isAuthenticated = false
                    self?.currentUser = nil
                    // Clear all data when unauthenticated
                    self?.clearAllFields()
                    // Clear chat service data
                    Task { @MainActor in
                        ChatService.shared.clearAllData()
                    }
                case .pending:
                    break
                }
            }
            .store(in: &cancellables)
    }

    private func validateSignUpForm() -> Bool {
        guard !email.isEmpty, email.isValidEmail else {
            showError("Please enter a valid email address.")
            return false
        }

        guard !fullName.isEmpty else {
            showError("Please enter your full name.")
            return false
        }

        guard password.isValidPassword else {
            showError("Password must be at least \(AppConstants.Validation.minPasswordLength) characters long.")
            return false
        }

        return true
    }

    private func validateSignInForm() -> Bool {
        guard !email.isEmpty, email.isValidEmail else {
            showError("Please enter a valid email address.")
            return false
        }

        guard !password.isEmpty else {
            showError("Please enter your password.")
            return false
        }

        return true
    }

    func showSuccess(_ message: String) {
        successMessage = message
        showSuccessAlert = true
        triggerSuccessFeedback()
    }

    func showError(_ message: String) {
        errorMessage = message
        showErrorAlert = true
        triggerErrorFeedback()
    }

    private func clearErrorState() {
        // Only clear error message, don't auto-dismiss the alert
        errorMessage = nil
        // showErrorAlert will be set to false when user taps OK
    }

    private func triggerErrorFeedback() {
        errorTrigger.toggle()
    }

    private func clearSignUpFields() {
        email = ""
        password = ""
        fullName = ""
        phoneNumber = ""
    }

    private func clearSignInFields() {
        email = ""
        password = ""
    }
    
    private func clearAllFields() {
        email = ""
        password = ""
        fullName = ""
        phoneNumber = ""
        currentPassword = ""
        newEmail = ""
        newPassword = ""
        passwordConfirmation = ""
    }
}
