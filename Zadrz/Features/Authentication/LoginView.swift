//
//  LoginView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct LoginView: View {
    @ObservedObject var authViewModel: AuthenticationViewModel

    @FocusState private var focusedField: LoginField?
    @State private var navigateToInterests = false
    @State private var showForgotPassword = false
    
    var body: some View {
        VStack(spacing: 0) {
            LoginForm(
                authViewModel: authViewModel,
                focusedField: $focusedField,
                onLogin: handleLogin,
                onForgotPassword: { showForgotPassword = true }
            )
        }
        .onTapGesture {
            focusedField = nil
        }
        .onAppear {
            clearFields()
        }
        .task {
            focusedField = .email
        }
        .navigationDestination(isPresented: $navigateToInterests) {
            InterestsSelectionView()
        }
        .sheet(isPresented: $showForgotPassword) {
            NavigationStack {
                ForgotPasswordView(authViewModel: authViewModel)
            }
        }
        .sensoryFeedback(
            success: $authViewModel.successTrigger,
            error: $authViewModel.errorTrigger
        )
//        .alert("Success", isPresented: $authViewModel.showSuccessAlert) {
//            Button("Continue") {
//                clearFields()
//                onAuthenticated?()
//                navigateToInterests = true
//            }
//        } message: {
//            Text(authViewModel.successMessage ?? "")
//        }
        .alert("Error", isPresented: $authViewModel.showErrorAlert) {
            Button("OK") {
                authViewModel.showErrorAlert = false
            }
        } message: {
            Text(authViewModel.errorMessage ?? "")
        }
        .navigationTitle(AppConstants.Text.Auth.login)
    }
    
    // MARK: - Private Methods
    private func handleLogin() {
        focusedField = nil
        Task {
            let success = await authViewModel.signIn()
            if success {
                navigateToInterests = true
            }
        }
    }
    
    private func clearFields() {
        authViewModel.email = ""
        authViewModel.password = ""
    }
}

// MARK: - Login Form
private struct LoginForm: View {
    @ObservedObject var authViewModel: AuthenticationViewModel
    @FocusState.Binding var focusedField: LoginField?
    let onLogin: () -> Void
    let onForgotPassword: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            VStack(spacing: 16) {
                FormTextField(
                    title: "Email",
                    placeholder: "<EMAIL>",
                    text: $authViewModel.email,
                    keyboardType: .emailAddress,
                    autocapitalization: .never,
                    errorMessage: emailErrorMessage
                )
                .focused($focusedField, equals: .email)
                .onSubmit {
                    focusedField = .password
                }
                
                FormTextField(
                    title: "Password",
                    placeholder: "Enter your password",
                    text: $authViewModel.password,
                    isSecure: true,
                    errorMessage: passwordErrorMessage
                )
                .focused($focusedField, equals: .password)
                .onSubmit(onLogin)
                
                HStack {
                    Spacer()
                    Button(AppConstants.Text.Auth.forgotPassword) {
                        onForgotPassword()
                    }
                    .font(.subheadline)
                    .foregroundColor(.accentColor)
                }
            }
            .standardPadding()
            
            PrimaryButton(
                title: AppConstants.Text.Auth.login,
                isLoading: authViewModel.isEmailSignInLoading,
                isEnabled: isFormValid
            ) {
                onLogin()
            }
            .standardPadding()
            .padding(.bottom, 32)
        }
    }
    
    // MARK: - Computed Properties
    private var isFormValid: Bool {
        !authViewModel.email.isEmpty && !authViewModel.password.isEmpty
    }
    
    private var emailErrorMessage: String? {
        guard !authViewModel.email.isEmpty else { return nil }
        return authViewModel.email.isValidEmail ? nil : "Please enter a valid email address"
    }
    
    private var passwordErrorMessage: String? {
        guard !authViewModel.password.isEmpty else { return nil }
        return authViewModel.password.isValidPassword ? nil : "Password must be at least \(AppConstants.Validation.minPasswordLength) characters"
    }
}

// MARK: - Login Field
private enum LoginField {
    case email, password
}

#Preview {
    NavigationStack {
        LoginView(authViewModel: AuthenticationViewModel())
    }
}
