//
//  AppRootView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct AppRootView: View {
    @StateObject private var authViewModel = AuthenticationViewModel()
    @State private var isInitializing = true

    var body: some View {
        ZStack {
            if isInitializing {
                SplashLoadingView()
                    .task {
                        await initializeApp()
                    }
            } else {
                // Purely reactive to AuthenticationViewModel's state
                if authViewModel.isLoading && !authViewModel.isAuthenticated {
                    AuthLoadingView()
                } else if authViewModel.isAuthenticated {
                    MainTabView(authViewModel: authViewModel)
                        .transition(.asymmetric(
                            insertion: .opacity.combined(with: .move(edge: .trailing)),
                            removal: .opacity.combined(with: .move(edge: .leading))
                        ))
                } else {
                    GreetingView(authViewModel: authViewModel)
                        .transition(.asymmetric(
                            insertion: .opacity.combined(with: .move(edge: .bottom)),
                            removal: .opacity.combined(with: .move(edge: .top))
                        ))
                }
            }
        }
        .animation(.easeInOut(duration: 0.5), value: authViewModel.isAuthenticated)
        .animation(.easeInOut(duration: 0.5), value: isInitializing)
        .onChange(of: authViewModel.isAuthenticated) { _, isAuthenticated in
            if isAuthenticated {
                // SavedMessagesService will be configured in ChatsView when user loads chats
                // This ensures consistent user ID usage across the app
            } else {
                // Clean up when user logs out
                SavedMessagesService.shared.cleanup()
            }
        }
    }

    // MARK: - Private Methods
    private func initializeApp() async {
        // Perform authentication check without artificial delay
        await authViewModel.tryAutoLogin()

        await MainActor.run {
            withAnimation(.easeInOut(duration: 0.5)) {
                isInitializing = false
            }
        }
    }
}

// MARK: - Loading Views
private struct SplashLoadingView: View {
    var body: some View {
        VStack(spacing: 24) {
            // App logo/icon placeholder
            ZStack {
                Circle()
                    .fill(Color.accentColor.gradient)
                    .frame(width: 80, height: 80)

                Image(systemName: "message.fill")
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(.white)
            }

            VStack(spacing: 8) {
                Text("Zadrz")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text("Connecting minds, one conversation at a time")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            ProgressView()
                .scaleEffect(1.2)
                .tint(.accentColor)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(.ultraThinMaterial)
    }
}

private struct AuthLoadingView: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(.accentColor)

            Text("Authenticating...")
                .font(.headline)
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(.ultraThinMaterial)
    }
}

#Preview {
    AppRootView()
}
