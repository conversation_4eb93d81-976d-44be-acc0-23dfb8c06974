//
//  AppRootView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct AppRootView: View {
    @StateObject private var authViewModel = AuthenticationViewModel()
    @State private var isAuthenticated: Bool? = nil
    
    var body: some View {
        Group {
            if isAuthenticated == nil {
                LoadingView()
                    .task {
                         await authViewModel.tryAutoLogin()
                        isAuthenticated = true
                    }
            } else if isAuthenticated == false {
                GreetingView(authViewModel: authViewModel) {
                    // Don't manually set authentication state, let the auth service handle it
                    isAuthenticated = true
                }
            } else {
                MainTabView(authViewModel: authViewModel)
            }
        }
        .onReceive(authViewModel.$isAuthenticated) { value in
            // isAuthenticated = value
        }
        .onReceive(authViewModel.$didLogout) { didLogout in
            if didLogout {
                // Force return to login screen on logout
                isAuthenticated = false
            }
        }
    }
}

// MARK: - Loading View
private struct LoadingView: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Loading...")
                .font(.headline)
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(.ultraThinMaterial)
    }
}

#Preview {
    AppRootView()
}
