//
//  MainTabView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct MainTabView: View {
    @ObservedObject var authViewModel: AuthenticationViewModel
    @StateObject private var navigationRouter = NavigationRouter()
    @State private var selectedTab: TabItem = .chats

    var body: some View {
        TabView(selection: $selectedTab) {
            ChatsView()
                .tabItem {
                    Label(TabItem.chats.title, systemImage: TabItem.chats.icon)
                }
                .tag(TabItem.chats)

            HeroSelectionView()
                .tabItem {
                    Label("Heroes", systemImage: "person.2.fill")
                }
                .tag(TabItem.avatars)

            ProfileView(authViewModel: authViewModel)
                .tabItem {
                    Label(TabItem.profile.title, systemImage: TabItem.profile.icon)
                }
                .tag(TabItem.profile)
        }
        .accentColor(.accentColor)
        .environmentObject(navigationRouter)
        .onChange(of: selectedTab) { _, newTab in
            navigationRouter.selectedTab = newTab
        }
        .onChange(of: navigationRouter.selectedTab) { _, newTab in
            selectedTab = newTab
        }
    }

    // MARK: - Tab Content
    // Removed tabContent function to make the changes

}

#Preview {
    MainTabView(authViewModel: AuthenticationViewModel())
}
