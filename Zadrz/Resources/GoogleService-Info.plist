<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>162291819018-4q69erkieto5ov8t7cvn36l230nknkph.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.162291819018-4q69erkieto5ov8t7cvn36l230nknkph</string>
	<key>API_KEY</key>
	<string>AIzaSyDZ7lRX2qTpYNWxjhwxYMOM1V5PshNsDY0</string>
	<key>GCM_SENDER_ID</key>
	<string>162291819018</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.didar.Zadrz</string>
	<key>PROJECT_ID</key>
	<string>zadrz-edb33</string>
	<key>STORAGE_BUCKET</key>
	<string>zadrz-edb33.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false/>
	<key>IS_ANALYTICS_ENABLED</key>
	<false/>
	<key>IS_APPINVITE_ENABLED</key>
	<true/>
	<key>IS_GCM_ENABLED</key>
	<true/>
	<key>IS_SIGNIN_ENABLED</key>
	<true/>
	<key>GOOGLE_APP_ID</key>
	<string>1:162291819018:ios:723631ae4725c69048f5f3</string>
	<key>DATABASE_URL</key>
	<string>https://zadrz-edb33-default-rtdb.firebaseio.com</string>
</dict>
</plist>
