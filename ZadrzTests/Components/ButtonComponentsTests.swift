//
//  ButtonComponentsTests.swift
//  ZadrzTests
//
//  Created by <PERSON><PERSON><PERSON> on 6/22/25.
//

import Testing
import SwiftUI
@testable import Zadrz

/// Tests for enhanced button components with customization options
struct ButtonComponentsTests {
    
    // MARK: - PrimaryButton Tests
    
    @Test("PrimaryButton default initialization")
    func testPrimaryButtonDefaultInitialization() {
        let button = PrimaryButton(title: "Test Button") {}
        
        #expect(button.title == "Test Button")
        #expect(button.icon == nil)
        #expect(button.backgroundColor == .accentColor)
        #expect(button.foregroundColor == .black)
        #expect(button.isLoading == false)
        #expect(button.isEnabled == true)
    }
    
    @Test("PrimaryButton custom initialization")
    func testPrimaryButtonCustomInitialization() {
        let button = PrimaryButton(
            title: "Custom Button",
            icon: "star.fill",
            backgroundColor: .red,
            foregroundColor: .white,
            isLoading: true,
            isEnabled: false
        ) {}
        
        #expect(button.title == "Custom Button")
        #expect(button.icon == "star.fill")
        #expect(button.backgroundColor == .red)
        #expect(button.foregroundColor == .white)
        #expect(button.isLoading == true)
        #expect(button.isEnabled == false)
    }
    
    // MARK: - SecondaryButton Tests
    
    @Test("SecondaryButton default initialization")
    func testSecondaryButtonDefaultInitialization() {
        let button = SecondaryButton(title: "Test Button") {}
        
        #expect(button.title == "Test Button")
        #expect(button.icon == nil)
        #expect(button.backgroundColor == .clear)
        #expect(button.foregroundColor == .accentColor)
        #expect(button.borderColor == .accentColor)
        #expect(button.isLoading == false)
        #expect(button.isEnabled == true)
    }
    
    @Test("SecondaryButton custom initialization")
    func testSecondaryButtonCustomInitialization() {
        let button = SecondaryButton(
            title: "Custom Button",
            icon: "plus.circle",
            backgroundColor: .blue.opacity(0.1),
            foregroundColor: .blue,
            borderColor: .blue,
            isLoading: true,
            isEnabled: false
        ) {}
        
        #expect(button.title == "Custom Button")
        #expect(button.icon == "plus.circle")
        #expect(button.backgroundColor == .blue.opacity(0.1))
        #expect(button.foregroundColor == .blue)
        #expect(button.borderColor == .blue)
        #expect(button.isLoading == true)
        #expect(button.isEnabled == false)
    }
    
    // MARK: - CapsuleButton Tests
    
    @Test("CapsuleButton default initialization")
    func testCapsuleButtonDefaultInitialization() {
        let button = CapsuleButton(title: "Test Button", isSelected: false) {}
        
        #expect(button.title == "Test Button")
        #expect(button.icon == nil)
        #expect(button.selectedBackgroundColor == .accentColor)
        #expect(button.unselectedBackgroundColor == .clear)
        #expect(button.selectedForegroundColor == .black)
        #expect(button.unselectedForegroundColor == .accentColor)
        #expect(button.borderColor == .accentColor)
        #expect(button.isSelected == false)
    }
    
    @Test("CapsuleButton custom initialization")
    func testCapsuleButtonCustomInitialization() {
        let button = CapsuleButton(
            title: "Custom Button",
            icon: "heart.fill",
            selectedBackgroundColor: .green,
            unselectedBackgroundColor: .gray.opacity(0.1),
            selectedForegroundColor: .white,
            unselectedForegroundColor: .green,
            borderColor: .green,
            isSelected: true
        ) {}
        
        #expect(button.title == "Custom Button")
        #expect(button.icon == "heart.fill")
        #expect(button.selectedBackgroundColor == .green)
        #expect(button.unselectedBackgroundColor == .gray.opacity(0.1))
        #expect(button.selectedForegroundColor == .white)
        #expect(button.unselectedForegroundColor == .green)
        #expect(button.borderColor == .green)
        #expect(button.isSelected == true)
    }
    
    // MARK: - Backward Compatibility Tests
    
    @Test("PrimaryButton backward compatibility")
    func testPrimaryButtonBackwardCompatibility() {
        // Test that existing usage patterns still work
        let button1 = PrimaryButton(title: "Button") {}
        let button2 = PrimaryButton(title: "Loading", isLoading: true) {}
        let button3 = PrimaryButton(title: "Disabled", isEnabled: false) {}
        
        #expect(button1.title == "Button")
        #expect(button2.isLoading == true)
        #expect(button3.isEnabled == false)
    }
    
    @Test("SecondaryButton backward compatibility")
    func testSecondaryButtonBackwardCompatibility() {
        // Test that existing usage patterns still work
        let button1 = SecondaryButton(title: "Button") {}
        let button2 = SecondaryButton(title: "Loading", isLoading: true) {}
        let button3 = SecondaryButton(title: "Disabled", isEnabled: false) {}
        
        #expect(button1.title == "Button")
        #expect(button2.isLoading == true)
        #expect(button3.isEnabled == false)
    }
    
    @Test("CapsuleButton backward compatibility")
    func testCapsuleButtonBackwardCompatibility() {
        // Test that existing usage patterns still work
        let button1 = CapsuleButton(title: "Selected", isSelected: true) {}
        let button2 = CapsuleButton(title: "Unselected", isSelected: false) {}
        
        #expect(button1.title == "Selected")
        #expect(button1.isSelected == true)
        #expect(button2.title == "Unselected")
        #expect(button2.isSelected == false)
    }
}
